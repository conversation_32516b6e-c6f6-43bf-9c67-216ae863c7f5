import logging
import os
import time
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional

import requests
from pinecone import Pinecone
from requests.adapters import HTT<PERSON>dapter
from sentence_transformers import SentenceTransformer
from urllib3.util.retry import Retry

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("update.log"), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


@dataclass
class Config:
    """Configuration class for API endpoints and settings"""

    COMPONENT_URL: str = (
        "https://app-dev.rapidinnovation.dev/api/v1/components?refresh=true"
    )
    MCP_URL: str = "https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps/{}"
    WORKFLOW_URL: str = (
        "https://app-dev.rapidinnovation.dev/api/v1/marketplace/workflows/{}"
    )
    MCP_LIST_URL: str = "https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps"
    WORKFLOW_LIST_URL: str = (
        "https://app-dev.rapidinnovation.dev/api/v1/marketplace/workflows"
    )
    PAGE_SIZE: int = 10
    REQUEST_TIMEOUT: int = 30
    MAX_RETRIES: int = 3
    BACKOFF_FACTOR: float = 0.3


config = Config()

# Environment variables
PINECONE_API_KEY = os.environ.get("PINECONE_API_KEY")
if not PINECONE_API_KEY:
    logger.error("PINECONE_API_KEY environment variable is not set")
    raise ValueError("PINECONE_API_KEY environment variable is required")

# Initialize Pinecone and embedding model
try:
    logger.info("Initializing Pinecone client...")
    pc = Pinecone(PINECONE_API_KEY)
    index = pc.Index("tool-embeddings")
    logger.info("Pinecone client initialized successfully")

    logger.info("Loading embedding model...")
    embedding_model = SentenceTransformer("mixedbread-ai/mxbai-embed-large-v1")
    logger.info("Embedding model loaded successfully")
except Exception as e:
    logger.error(f"Failed to initialize Pinecone or embedding model: {e}")
    raise


# Configure requests session with retry strategy
def create_session() -> requests.Session:
    """Create a requests session with retry strategy and timeout"""
    session = requests.Session()

    retry_strategy = Retry(
        total=config.MAX_RETRIES,
        backoff_factor=config.BACKOFF_FACTOR,
        status_forcelist=[429, 500, 502, 503, 504],
    )

    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    return session


session = create_session()


def get_embedding(data: Dict[str, Any]) -> List[float]:
    """Generate embedding for the given data"""
    try:
        sent = f"Name : {data['name']}\nDescription : {data['description']}"
        if data["type"] == "mcp":
            sent += f"\nMCP Name : {data['mcp_name']}"
        if data.get("category") is not None:
            sent += f"\nCategory : {data['category']}"
        else:
            sent += f"\nCategory : general"

        logger.debug(f"Generating embedding for: {data['name']}")
        embedding = embedding_model.encode(sent).tolist()
        logger.debug(f"Successfully generated embedding for: {data['name']}")
        return embedding
    except Exception as e:
        logger.error(
            f"Failed to generate embedding for {data.get('name', 'unknown')}: {e}"
        )
        raise


def safe_api_request(url: str, timeout: int = None) -> Optional[Dict[str, Any]]:
    """Make a safe API request with error handling and logging"""
    timeout = timeout or config.REQUEST_TIMEOUT
    try:
        logger.debug(f"Making API request to: {url}")
        response = session.get(url, timeout=timeout)
        response.raise_for_status()
        logger.debug(f"Successfully received response from: {url}")
        return response.json()
    except requests.exceptions.Timeout:
        logger.error(f"Request timeout for URL: {url}")
        return None
    except requests.exceptions.RequestException as e:
        logger.error(f"Request failed for URL {url}: {e}")
        return None
    except ValueError as e:
        logger.error(f"Failed to parse JSON response from {url}: {e}")
        return None


def update_element(data: Dict[str, Any]) -> bool:
    """Update or insert an element in the Pinecone index"""
    try:
        # Generate unique ID based on element type
        if data["type"] == "component":
            id_ = f"component_{data['name']}"
        elif data["type"] == "mcp":
            id_ = f"mcp_{data['id']}_{data['name']}"
        elif data["type"] == "workflow":
            id_ = f"workflow_{data['id']}"
        else:
            logger.error(f"Unknown element type: {data['type']}")
            return False

        logger.debug(f"Processing element: {id_}")

        # Fetch existing vector from Pinecone
        result = index.fetch(ids=[id_])

        if len(result.vectors) == 0:
            # New element - insert it
            logger.info(f"Inserting new element: {id_}")
            embedding = get_embedding(data)
            metadata = {k: v for k, v in data.items() if v is not None}
            index.upsert(vectors=[(id_, embedding, metadata)])
            logger.info(f"Successfully inserted element: {id_}")
            return True
        else:
            # Element exists - check if update is needed
            result = result.vectors[id_].metadata
            if data["type"] == "component":
                logger.debug(f"Component {id_} already exists, skipping update")
                return True

            if "updated_at" not in result:
                logger.warning(f"No updated_at field found for {id_}, skipping update")
                return True

            # Compare timestamps
            last_updated_str = result["updated_at"]
            current_updated_str = data["updated_at"]

            try:
                last_updated_date = datetime.fromisoformat(last_updated_str).replace(
                    tzinfo=None
                )
                current_updated_date = datetime.fromisoformat(
                    current_updated_str
                ).replace(tzinfo=None)

                if current_updated_date > last_updated_date and (
                    result["name"] != data["name"]
                    or result["description"] != data["description"]
                ):
                    logger.info(
                        f"Updating element: {id_} (last: {last_updated_str}, current: {current_updated_str})"
                    )
                    embedding = get_embedding(data)
                    metadata = {k: v for k, v in data.items() if v is not None}
                    index.update(id=id_, values=embedding, metadata=metadata)
                    logger.info(f"Successfully updated element: {id_}")
                    return True
                else:
                    logger.debug(f"Element {id_} is up to date")
                    return True

            except ValueError as e:
                logger.error(f"Failed to parse date for {id_}: {e}")
                return False

    except Exception as e:
        logger.error(f"Failed to update element {data.get('name', 'unknown')}: {e}")
        return False


def update_component() -> bool:
    """Update all components from the API"""
    logger.info("Starting component update process...")

    try:
        components_data = safe_api_request(config.COMPONENT_URL)
        if not components_data:
            logger.error("Failed to fetch components data")
            return False

        total_components = sum(
            len(components_data[category]) for category in components_data
        )
        logger.info(
            f"Found {total_components} components across {len(components_data)} categories"
        )

        processed_count = 0
        success_count = 0

        for category in components_data:
            logger.info(f"Processing category: {category}")

            for component_key in components_data[category]:
                component_data = components_data[category][component_key]

                loop_description = {
                    "type": "component",
                    "category": category,
                    "name": component_data["name"],
                    "description": component_data["description"],
                }

                if update_element(loop_description):
                    success_count += 1

                processed_count += 1

                if processed_count % 10 == 0:
                    logger.info(
                        f"Processed {processed_count}/{total_components} components"
                    )

        logger.info(
            f"Component update completed: {success_count}/{processed_count} successful"
        )
        return success_count == processed_count

    except Exception as e:
        logger.error(f"Failed to update components: {e}")
        return False


def update_mcp() -> bool:
    """Update all MCPs from the API"""
    logger.info("Starting MCP update process...")

    try:
        # Get first page to determine total pages
        first_page_url = f"{config.MCP_LIST_URL}?page=1&page_size={config.PAGE_SIZE}"
        first_page_data = safe_api_request(first_page_url)
        if not first_page_data:
            logger.error("Failed to fetch MCP list")
            return False

        total_pages = first_page_data["metadata"]["total_pages"]
        logger.info(f"Found {total_pages} pages of MCPs to process")

        processed_count = 0
        success_count = 0

        for page in range(1, total_pages + 1):
            logger.info(f"Processing MCP page {page}/{total_pages}")

            page_url = f"{config.MCP_LIST_URL}?page={page}&page_size={config.PAGE_SIZE}"
            mcps_data = safe_api_request(page_url)
            if not mcps_data:
                logger.error(f"Failed to fetch MCP page {page}")
                continue

            for mcp in mcps_data["data"]:
                mcp_id = mcp["id"]
                logger.debug(f"Processing MCP: {mcp_id}")

                # Get detailed MCP data
                mcp_detail_url = config.MCP_URL.format(mcp_id)
                mcp_data = safe_api_request(mcp_detail_url)
                if not mcp_data:
                    logger.error(f"Failed to fetch MCP details for {mcp_id}")
                    continue

                mcp_info = mcp_data["mcp"]
                tools = mcp_info.get("mcp_tools_config", {}).get("tools", [])

                logger.debug(f"Found {len(tools)} tools for MCP {mcp_info['name']}")

                for tool in tools:
                    loop_description = {
                        "type": "mcp",
                        "id": mcp_info["id"],
                        "mcp_name": mcp_info["name"],
                        "updated_at": mcp_info["updated_at"],
                        "category": mcp_info["category"],
                        "name": tool["name"],
                        "description": tool["description"],
                    }

                    if update_element(loop_description):
                        success_count += 1

                    processed_count += 1

        logger.info(
            f"MCP update completed: {success_count}/{processed_count} successful"
        )
        return success_count == processed_count

    except Exception as e:
        logger.error(f"Failed to update MCPs: {e}")
        return False


def update_workflow() -> bool:
    """Update all workflows from the API"""
    logger.info("Starting workflow update process...")

    try:
        # Get first page to determine total pages
        first_page_url = (
            f"{config.WORKFLOW_LIST_URL}?page=1&page_size={config.PAGE_SIZE}"
        )
        first_page_data = safe_api_request(first_page_url)
        if not first_page_data:
            logger.error("Failed to fetch workflow list")
            return False

        total_pages = first_page_data["metadata"]["total_pages"]
        logger.info(f"Found {total_pages} pages of workflows to process")

        processed_count = 0
        success_count = 0

        for page in range(1, total_pages + 1):
            logger.info(f"Processing workflow page {page}/{total_pages}")

            page_url = (
                f"{config.WORKFLOW_LIST_URL}?page={page}&page_size={config.PAGE_SIZE}"
            )
            workflows_data = safe_api_request(page_url)
            if not workflows_data:
                logger.error(f"Failed to fetch workflow page {page}")
                continue

            for workflow in workflows_data["data"]:
                workflow_id = workflow["id"]
                logger.debug(f"Processing workflow: {workflow_id}")

                # Get detailed workflow data
                workflow_detail_url = config.WORKFLOW_URL.format(workflow_id)
                workflow_data = safe_api_request(workflow_detail_url)
                if not workflow_data:
                    logger.error(f"Failed to fetch workflow details for {workflow_id}")
                    continue

                workflow_info = workflow_data["workflow"]

                loop_description = {
                    "type": "workflow",
                    "id": workflow_info["id"],
                    "name": workflow_info["name"],
                    "category": workflow_info["category"],
                    "description": workflow_info["description"],
                    "updated_at": workflow_info["updated_at"],
                }

                if update_element(loop_description):
                    success_count += 1

                processed_count += 1

        logger.info(
            f"Workflow update completed: {success_count}/{processed_count} successful"
        )
        return success_count == processed_count

    except Exception as e:
        logger.error(f"Failed to update workflows: {e}")
        return False


def main() -> None:
    """Main execution function with comprehensive logging and error handling"""
    start_time = time.time()
    logger.info("=" * 60)
    logger.info("Starting RAG deployment update process")
    logger.info("=" * 60)

    results = {"components": False, "mcps": False, "workflows": False}

    try:
        # Update components
        logger.info("Phase 1: Updating components...")
        results["components"] = update_component()

        # Update MCPs
        logger.info("Phase 2: Updating MCPs...")
        results["mcps"] = update_mcp()

        # Update workflows
        logger.info("Phase 3: Updating workflows...")
        results["workflows"] = update_workflow()

        # Summary
        elapsed_time = time.time() - start_time
        successful_phases = sum(results.values())
        total_phases = len(results)

        logger.info("=" * 60)
        logger.info("UPDATE PROCESS COMPLETED")
        logger.info("=" * 60)
        logger.info(f"Total execution time: {elapsed_time:.2f} seconds")
        logger.info(f"Successful phases: {successful_phases}/{total_phases}")

        for phase, success in results.items():
            status = "✓ SUCCESS" if success else "✗ FAILED"
            logger.info(f"  {phase.capitalize()}: {status}")

        if successful_phases == total_phases:
            logger.info("All update phases completed successfully!")
        else:
            logger.warning(
                f"{total_phases - successful_phases} phase(s) failed. Check logs for details."
            )

    except KeyboardInterrupt:
        logger.info("Update process interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error during update process: {e}")
        raise
    finally:
        logger.info("=" * 60)


if __name__ == "__main__":
    main()
