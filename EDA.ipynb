import pandas as pd

df = pd.read_json("description.json")

df.head()

df.value_counts("type")

df[df["description"].isna()]

import os
from pinecone import Pinecone
PINECONE_API_KEY = os.environ.get("PINECONE_API_KEY")
pc = Pinecone(PINECONE_API_KEY)
index = pc.Index("tool-embeddings")

data = {
        "type": "component",
        "category": "AI",
        "name": "AgenticAI",
        "description": "Executes an AI agent with tools and memory using AutoGen."}

if data["type"] == "component":
    id_ = f"component_{data['name']}"
elif data["type"] == "mcp":
    id_ = f"mcp_{data['id']}_{data['name']}"
elif data["type"] == "workflow":
    id_ = f"workflow_{data['id']}"

result = index.fetch(ids=[id_])

result.vectors[id_].metadata

result["vectors"][id_]["metadata"]

