{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8e0cbbc2", "metadata": {}, "outputs": [], "source": ["import json\n", "from pinecone import Pinecone, ServerlessSpec"]}, {"cell_type": "code", "execution_count": 2, "id": "0a9c48fd", "metadata": {}, "outputs": [], "source": ["pc = Pinecone(\"pcsk_2BkTDm_5joJfVaNA5ahcWFQ7uVcpWUbrstBJj1ipAYVcmZnR8FDD1cEHKcSACvBqw7PeT7\")"]}, {"cell_type": "code", "execution_count": 4, "id": "ade5e7b2", "metadata": {}, "outputs": [{"data": {"text/plain": ["{\n", "    \"name\": \"tool-embeddings\",\n", "    \"metric\": \"dotproduct\",\n", "    \"host\": \"tool-embeddings-jxxa616.svc.aped-4627-b74a.pinecone.io\",\n", "    \"spec\": {\n", "        \"serverless\": {\n", "            \"cloud\": \"aws\",\n", "            \"region\": \"us-east-1\"\n", "        }\n", "    },\n", "    \"status\": {\n", "        \"ready\": true,\n", "        \"state\": \"Ready\"\n", "    },\n", "    \"vector_type\": \"dense\",\n", "    \"dimension\": 1024,\n", "    \"deletion_protection\": \"disabled\",\n", "    \"tags\": null\n", "}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["pc.create_index(\n", "  name=\"tool-embeddings\",\n", "  vector_type=\"dense\",\n", "  dimension=1024,\n", "  metric=\"dotproduct\",\n", "  spec=ServerlessSpec(\n", "    cloud=\"aws\",\n", "    region=\"us-east-1\"\n", "  )\n", ")"]}, {"cell_type": "code", "execution_count": 5, "id": "aab57680", "metadata": {}, "outputs": [], "source": ["description = json.load(open(\"description_embeddings.json\", \"r\"))"]}, {"cell_type": "code", "execution_count": 10, "id": "6ef14ae6", "metadata": {}, "outputs": [], "source": ["data_pc = []\n", "for i in range(len(description)):\n", "    data = description[i]\n", "    if data[\"type\"] == \"component\":\n", "        id_ = f\"component_{data['name']}\"\n", "    elif data[\"type\"] == \"mcp\":\n", "        id_ = f\"mcp_{data['id']}_{data['name']}\"\n", "    elif data[\"type\"] == \"workflow\":\n", "        id_ = f\"workflow_{data['id']}\"\n", "    value = data[\"embedding\"]\n", "    metadata = {k:v for k,v in data.items() if k != \"embedding\" and v is not None}\n", "    data_pc.append({\"id\": id_, \"values\": value, \"metadata\": metadata})"]}, {"cell_type": "code", "execution_count": 11, "id": "1d4da45a", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'upserted_count': 554}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["index = pc.Index(\"tool-embeddings\")\n", "index.upsert(data_pc)"]}, {"cell_type": "code", "execution_count": 12, "id": "00e6707b", "metadata": {}, "outputs": [], "source": ["from sentence_transformers import SentenceTransformer\n", "embedding_model = SentenceTransformer(\"mixedbread-ai/mxbai-embed-large-v1\")"]}, {"cell_type": "code", "execution_count": 39, "id": "d7eb7de4", "metadata": {}, "outputs": [], "source": ["query = \"Executes an AI agent with tools and memory using AutoGen.\"\n", "query_embedding = embedding_model.encode(query).tolist()"]}, {"cell_type": "code", "execution_count": 40, "id": "1dc86aa4", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'matches': [{'id': 'component_AgenticAI',\n", "              'metadata': {'category': 'AI',\n", "                           'description': 'Executes an AI agent with tools and '\n", "                                          'memory using AutoGen.',\n", "                           'name': 'AgenticAI',\n", "                           'type': 'component'},\n", "              'score': 249.057617,\n", "              'values': []},\n", "             {'id': 'mcp_de313c10-d664-49e0-889e-e41e0230f704_generate_ai_stock_video',\n", "              'metadata': {'category': 'marketing',\n", "                           'description': 'generate the ai stock video using '\n", "                                          'the script',\n", "                           'id': 'de313c10-d664-49e0-889e-e41e0230f704',\n", "                           'mcp_name': 'stock-video-generation-mcp',\n", "                           'name': 'generate_ai_stock_video',\n", "                           'type': 'mcp',\n", "                           'updated_at': '2025-08-19T13:34:36.669597'},\n", "              'score': 193.109619,\n", "              'values': []},\n", "             {'id': 'workflow_b5e46aa2-ec27-4239-a547-af42f8b4375d',\n", "              'metadata': {'description': 'JD_Creation_-_(<PERSON>-<PERSON><PERSON>)_',\n", "                           'id': 'b5e46aa2-ec27-4239-a547-af42f8b4375d',\n", "                           'name': '<PERSON><PERSON> Creation - (<PERSON>-<PERSON><PERSON>) ',\n", "                           'type': 'workflow',\n", "                           'updated_at': '2025-09-01T10:06:31.617730'},\n", "              'score': 190.433105,\n", "              'values': []},\n", "             {'id': 'workflow_4fdde5aa-6911-4bda-8123-f94e36e3afed',\n", "              'metadata': {'description': 'Website_Generator',\n", "                           'id': '4fdde5aa-6911-4bda-8123-f94e36e3afed',\n", "                           'name': 'Website Generator',\n", "                           'type': 'workflow',\n", "                           'updated_at': '2025-08-25T11:56:24.901012'},\n", "              'score': 184.383789,\n", "              'values': []},\n", "             {'id': 'mcp_796faf92-17eb-4c8f-bd22-185418425862_create_file',\n", "              'metadata': {'category': 'general',\n", "                           'description': '\\n'\n", "                                          '    Create a new file in the '\n", "                                          'project with content generated by '\n", "                                          'the AI Agent.\\n'\n", "                                          '\\n'\n", "                                          '    This tool performs the '\n", "                                          'following steps:\\n'\n", "                                          '    1. Get the file_name, file_path '\n", "                                          'and content from the AI Agent\\n'\n", "                                          '    2. Create a new file with the '\n", "                                          'given file_name at the file_path\\n'\n", "                                          '    3. Write the content in the '\n", "                                          'file\\n'\n", "                                          '\\n'\n", "                                          '    Args:\\n'\n", "                                          '        file_name: Name of the file '\n", "                                          'to create (e.g., \"index.html\", '\n", "                                          '\"app.js\")\\n'\n", "                                          '        file_path: Directory path '\n", "                                          'where the file should be created '\n", "                                          '(e.g., \"./src\", \"./public\")\\n'\n", "                                          '        content: Content to write '\n", "                                          'to the file\\n'\n", "                                          '\\n'\n", "                                          '    Returns:\\n'\n", "                                          '        Status message indicating '\n", "                                          'success or failure\\n'\n", "                                          '    ',\n", "                           'id': '796faf92-17eb-4c8f-bd22-185418425862',\n", "                           'mcp_name': 'Website Generator',\n", "                           'name': 'create_file',\n", "                           'type': 'mcp',\n", "                           'updated_at': '2025-07-24T16:59:29.371058'},\n", "              'score': 181.023682,\n", "              'values': []},\n", "             {'id': 'mcp_0dc83245-794f-405d-8814-7771260d3c60_script_generate',\n", "              'metadata': {'category': 'general',\n", "                           'description': 'Provide topic and keyword to '\n", "                                          'generator Script',\n", "                           'id': '0dc83245-794f-405d-8814-7771260d3c60',\n", "                           'mcp_name': 'Script Generation',\n", "                           'name': 'script_generate',\n", "                           'type': 'mcp',\n", "                           'updated_at': '2025-07-25T12:21:58.170822'},\n", "              'score': 179.665527,\n", "              'values': []},\n", "             {'id': 'mcp_f506eb7c-a648-4873-8055-e2273be08478_execute_code_with_variables',\n", "              'metadata': {'category': 'engineering',\n", "                           'description': 'Execute JavaScript or Python code '\n", "                                          'with dynamic input variables that '\n", "                                          'can be defined and passed as '\n", "                                          'key-value pairs',\n", "                           'id': 'f506eb7c-a648-4873-8055-e2273be08478',\n", "                           'mcp_name': 'Code-Runner-Mcp',\n", "                           'name': 'execute_code_with_variables',\n", "                           'type': 'mcp',\n", "                           'updated_at': '2025-08-22T10:06:33.574903'},\n", "              'score': 177.361084,\n", "              'values': []},\n", "             {'id': 'mcp_ce1e8436-63b7-4f64-86b0-3cecc3bbd05b_generate_script',\n", "              'metadata': {'category': 'general',\n", "                           'description': 'Generate a script using OpenAI '\n", "                                          'GPT-4o.',\n", "                           'id': 'ce1e8436-63b7-4f64-86b0-3cecc3bbd05b',\n", "                           'mcp_name': 'script-generation-mcp-server',\n", "                           'name': 'generate_script',\n", "                           'type': 'mcp',\n", "                           'updated_at': '2025-08-13T06:19:06.140863'},\n", "              'score': 176.585449,\n", "              'values': []},\n", "             {'id': 'mcp_a1700776-e64f-4270-9e4e-3f7a85383919_script_generate',\n", "              'metadata': {'category': 'marketing',\n", "                           'description': 'Provide topic and keyword to '\n", "                                          'generator Script',\n", "                           'id': 'a1700776-e64f-4270-9e4e-3f7a85383919',\n", "                           'mcp_name': 'script generation',\n", "                           'name': 'script_generate',\n", "                           'type': 'mcp',\n", "                           'updated_at': '2025-07-14T11:42:37.361221'},\n", "              'score': 175.719238,\n", "              'values': []},\n", "             {'id': 'mcp_748a8221-d7d9-4352-93ae-00700f4d28b1_script_generate',\n", "              'metadata': {'category': 'marketing',\n", "                           'description': 'Provide topic and keyword to '\n", "                                          'generator Script',\n", "                           'id': '748a8221-d7d9-4352-93ae-00700f4d28b1',\n", "                           'mcp_name': 'script-generation-mcp',\n", "                           'name': 'script_generate',\n", "                           'type': 'mcp',\n", "                           'updated_at': '2025-08-11T14:40:09.066178'},\n", "              'score': 174.58667,\n", "              'values': []}],\n", " 'namespace': '',\n", " 'usage': {'read_units': 1}}"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["index.query(vector=query_embedding, top_k=10, include_metadata=True)\n"]}, {"cell_type": "code", "execution_count": null, "id": "adb90d20", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}