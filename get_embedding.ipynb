{"cells": [{"cell_type": "code", "execution_count": 1, "id": "01592ddf", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/lib/python3.13/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import json\n", "from sentence_transformers import SentenceTransformer"]}, {"cell_type": "code", "execution_count": 2, "id": "c5e3df54", "metadata": {}, "outputs": [], "source": ["with open(\"description.json\", \"r\") as f:\n", "    description = json.load(f)"]}, {"cell_type": "code", "execution_count": 3, "id": "52b15593", "metadata": {}, "outputs": [{"data": {"text/plain": ["554"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["len(description)"]}, {"cell_type": "code", "execution_count": 4, "id": "f9b82ab2", "metadata": {}, "outputs": [], "source": ["embedding_model = SentenceTransformer(\"mixedbread-ai/mxbai-embed-large-v1\")"]}, {"cell_type": "code", "execution_count": 13, "id": "5cfcd1fc", "metadata": {}, "outputs": [], "source": ["\n", "def get_embedding(data):\n", "    sent = f\"Name : {data[\"name\"]}\\nDescription : {data[\"description\"]}\"\n", "    if data[\"type\"] == \"mcp\":\n", "        sent += f\"\\nMCP Name : {data[\"mcp_name\"]}\"\n", "    if data[\"category\"] is not None:\n", "        sent += f\"\\nCategory : {data[\"category\"]}\"\n", "    return embedding_model.encode(sent).tolist()"]}, {"cell_type": "code", "execution_count": 15, "id": "4bfcab9d", "metadata": {}, "outputs": [], "source": ["for i in range(len(description)):\n", "    description[i][\"embedding\"] = get_embedding(description[i])"]}, {"cell_type": "code", "execution_count": 16, "id": "86f26889", "metadata": {}, "outputs": [], "source": ["with open(\"description_embeddings.json\", \"w\") as f:\n", "    json.dump(description, f, indent=4)"]}, {"cell_type": "code", "execution_count": null, "id": "765d611a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}