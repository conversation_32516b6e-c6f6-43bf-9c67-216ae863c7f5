{"data": [{"id": "a6afa109-be9f-4213-b6e9-6e24d4713140", "name": "Meeting Scheduler", "description": "Meeting_Scheduler", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/b4567a84-7619-4e06-af9d-aed98a2621da.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/f89e3f3e-2340-4725-8806-602418f03285.json", "start_nodes": [{"field": "worksheet_name", "type": "string", "transition_id": "transition-MCP_Google_Sheets_find_row-1754475554875"}], "owner_id": "55b1d790-e922-40ec-9da9-7f784894ab42", "owner_name": "<PERSON><PERSON>", "use_count": 1, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-09-01T09:59:09.939231", "updated_at": "2025-09-01T09:59:35.359743", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "19fe0fb6-1e9e-4e6c-9bfa-21b03b42e872", "name": "Meeting Rescheduler", "description": "Meeting_Rescheduler", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/561810ed-a2ca-42ec-99d6-7f9465e8134c.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/30178eb8-d651-4082-ac97-b896e6cdc9b4.json", "start_nodes": [{"field": "worksheet_name", "type": "string", "transition_id": "transition-MCP_Google_Sheets_find_row-1754982994772"}], "owner_id": "55b1d790-e922-40ec-9da9-7f784894ab42", "owner_name": "<PERSON><PERSON>", "use_count": 1, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-09-01T09:57:59.795556", "updated_at": "2025-09-01T09:58:17.977527", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "92244fba-e6db-47ea-ab4e-eb7aae829af1", "name": "Untitled Workflow", "description": "Untitled_Workflow", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/5e952703-329c-4d63-a0fc-e88ab4096065.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/e4a65100-5725-4ca0-ac09-667247bccfdd.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1752214388208"}], "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "owner_name": "<PERSON><PERSON>", "use_count": 0, "execution_count": 0, "average_rating": null, "category": "general", "tags": null, "version": "1.0.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-08-28T11:50:56.359272", "updated_at": "2025-08-28T11:50:56.359279", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "a54d799b-50bf-444a-9e0a-fea809443054", "name": "SDR Email Reply Workflow (trigger)", "description": "SDR_Email_Reply_Workflow_(trigger)", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/f744e0b3-6c85-4435-9dcd-5190fa3a1774.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/fbcd9522-5e54-4dd3-96f0-b58eee718ca9.json", "start_nodes": [{"field": "main_input", "type": "dict", "transition_id": "transition-MergeDataComponent-1755077432929"}, {"field": "from_email_address", "type": "string", "transition_id": "transition-MCP_SDR_Management_reply_email_from_customer-1755145274911"}, {"field": "to_email_address", "type": "string", "transition_id": "transition-MCP_SDR_Management_reply_email_from_customer-1755145274911"}, {"field": "subject", "type": "string", "transition_id": "transition-MCP_SDR_Management_reply_email_from_customer-1755145274911"}, {"field": "body", "type": "string", "transition_id": "transition-MCP_SDR_Management_reply_email_from_customer-1755145274911"}], "owner_id": "14902289-d9a4-43ec-8a31-989babf03fe3", "owner_name": "<PERSON><PERSON><PERSON><PERSON>", "use_count": 9, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-08-13T09:41:02.789280", "updated_at": "2025-08-22T10:06:09.469444", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "e9cc3f2f-e2aa-45b9-a162-0647d296b44e", "name": "SDR Contact Generation Flow", "description": "SDR_Contact_Generation_Flow", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/260d3707-d526-4d5a-b13d-0a83ff056deb.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/c1f48af4-7f51-4f4e-86f3-a6e2ee710b2e.json", "start_nodes": [{"field": "main_input", "type": "dict", "transition_id": "transition-MergeDataComponent-1755070220281"}, {"field": "input_data", "type": "multiline", "transition_id": "transition-ConditionalNode-1755017361324"}], "owner_id": "14902289-d9a4-43ec-8a31-989babf03fe3", "owner_name": "<PERSON><PERSON><PERSON><PERSON>", "use_count": 6, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-08-13T09:29:23.430770", "updated_at": "2025-08-21T13:02:49.109869", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "b1a7a223-e304-4f5c-b74c-a0421e8d0fd2", "name": "Screening (<PERSON>)", "description": "Screening_(<PERSON>_<PERSON><PERSON>)", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/6370bf59-5b7a-4d5b-817c-e7175fdc74e6.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/3ca8872c-dde9-4746-b862-ac93a2949c12.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1753435722849"}, {"field": "main_input", "type": "string", "transition_id": "transition-CombineTextComponent-1753780149499"}], "owner_id": "55b1d790-e922-40ec-9da9-7f784894ab42", "owner_name": "<PERSON><PERSON>", "use_count": 6, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-08-04T11:01:47.253053", "updated_at": "2025-09-01T10:10:00.452143", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "2a215231-bcff-4741-84b4-b91a1e781abd", "name": "Email Draft workflow", "description": "Email_Draft_workflow", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/ca61c808-0ef3-423b-b3f6-0823c1c61750.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/882f7add-d791-4993-960b-60de8c7c60b8.json", "start_nodes": [{"field": "to", "type": "string", "transition_id": "transition-MCP_Gmail_create_draft-1754036922757"}], "owner_id": "8222552d-2264-459d-9cec-4f16d693d714", "owner_name": "<PERSON><PERSON><PERSON> ", "use_count": 9, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-08-01T12:15:19.521895", "updated_at": "2025-08-31T07:18:24.414796", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "d55ce613-3f0a-4afa-a4f6-fffa9c942376", "name": "Blog Generation v5.5 - published", "description": "No description provided.", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/5bac6de3-7ef0-42a4-8c92-98a11ee87f85.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/37ac2ced-178c-4fb7-9d6f-d7446f17fb53.json", "start_nodes": null, "owner_id": "8da56b93-b442-4166-a8a1-b902bd38f363", "owner_name": "<PERSON><PERSON>", "use_count": 8, "execution_count": 0, "average_rating": null, "category": "general", "tags": null, "version": "1.0.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-08-01T11:33:27.077385", "updated_at": "2025-08-26T07:02:44.312067", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "b5e46aa2-ec27-4239-a547-af42f8b4375d", "name": "JD Creation - (Agent-<PERSON><PERSON>) ", "description": "JD_Creation_-_(<PERSON><PERSON><PERSON><PERSON>)_", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/77228d33-d3a4-4c7a-814e-13b3e4a13b1b.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/da08c5de-2ae5-473a-9fe7-0c0e3acfdad8.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1752566127607"}], "owner_id": "598f7395-c39a-4bfa-9996-0e3bff89047c", "owner_name": "<PERSON><PERSON><PERSON><PERSON>", "use_count": 5, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.2.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-31T11:49:11.618973", "updated_at": "2025-09-01T10:06:31.617730", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "f907eb17-76f9-4583-80be-6c6bfa757fc6", "name": "GET JDs (Agent-Chat)", "description": "GET_JDs_(Agent-Cha<PERSON>)", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/0a9a0fe9-92b3-45eb-9fab-4bf4758a7518.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/eaf2c64d-1b49-49d6-9d37-89b04c090f28.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1753962094088"}], "owner_id": "598f7395-c39a-4bfa-9996-0e3bff89047c", "owner_name": "<PERSON><PERSON><PERSON><PERSON>", "use_count": 4, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.2.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-31T11:48:59.588161", "updated_at": "2025-08-22T04:49:38.195110", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "46becf9e-4bbe-435f-bc7f-efce231d8609", "name": "Update Comapany Details", "description": "Update_Comapany_Details", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/e0343446-dce4-4f24-8474-149035db522c.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/130e0314-5133-4399-ba0b-807afd7fba3c.json", "start_nodes": [{"field": "search_value", "type": "string", "transition_id": "transition-MCP_Google_Sheets_find_row-1753341145105"}, {"field": "values", "type": "array", "transition_id": "transition-MCP_Google_Sheets_update_row-1753688390368"}], "owner_id": "55b1d790-e922-40ec-9da9-7f784894ab42", "owner_name": "<PERSON><PERSON>", "use_count": 3, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-31T08:10:06.359068", "updated_at": "2025-08-22T04:49:50.376310", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "aabd047c-7968-4c7c-a335-3d5502845751", "name": "Add Candidate", "description": "Add_Candidate", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/c2eea369-a526-4482-879f-086a97c44d99.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/9c8a0722-d9ca-45ac-b268-e100cce7e6f0.json", "start_nodes": [{"field": "worksheet_name", "type": "string", "transition_id": "transition-MCP_Google_Sheets_add_single_row-1753351326708"}, {"field": "row", "type": "array", "transition_id": "transition-MCP_Google_Sheets_add_single_row-1753351326708"}], "owner_id": "55b1d790-e922-40ec-9da9-7f784894ab42", "owner_name": "<PERSON><PERSON>", "use_count": 6, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-31T08:09:57.236291", "updated_at": "2025-09-01T12:56:59.806921", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "f40818d6-c05d-441c-b680-cb4eb6f6a524", "name": "JD By ID", "description": "JD_By_ID", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/95ed67fd-adce-4a03-b956-5a29a179f488.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/a2083e27-ed8f-459e-ada3-d0d1d156a260.json", "start_nodes": [{"field": "search_value", "type": "string", "transition_id": "transition-MCP_Google_Sheets_find_row-1753432277498"}], "owner_id": "55b1d790-e922-40ec-9da9-7f784894ab42", "owner_name": "<PERSON><PERSON>", "use_count": 3, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-31T08:09:43.364676", "updated_at": "2025-08-22T04:50:08.348322", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "72223fb7-0e89-465c-a477-cd695488cf8d", "name": "Update Job Status", "description": "Update_Job_Status", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/ac1e17a7-5b00-422a-b583-c6fe3d8b7203.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/c9afd89e-abdb-4bc0-abff-078a86caa3d6.json", "start_nodes": [{"field": "search_value", "type": "string", "transition_id": "transition-MCP_Google_Sheets_find_row-1753463636173"}, {"field": "main_input", "type": "string", "transition_id": "transition-CombineTextComponent-1753778938530"}], "owner_id": "55b1d790-e922-40ec-9da9-7f784894ab42", "owner_name": "<PERSON><PERSON>", "use_count": 4, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-31T08:09:33.759683", "updated_at": "2025-09-01T13:00:45.300185", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "bbc002e8-90da-4336-a6cb-c461abd23929", "name": "Blog Generation v5.5", "description": "Blog_Generation_v5.5", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/e0076656-f8a3-4f51-85ef-227ee69a24d7.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/8f1bf658-2df4-4450-b43c-f4b513588dca.json", "start_nodes": [{"field": "input_data", "type": "multiline", "transition_id": "transition-UniversalConverterComponent-1753068818880"}, {"field": "trigger", "type": "handle", "transition_id": "transition-IDGeneratorComponent-1753340852138"}, {"field": "trigger", "type": "handle", "transition_id": "transition-IDGeneratorComponent-1753341788889"}], "owner_id": "4f19b076-b555-4d69-ba27-59dc10ecbf58", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 5, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-30T10:32:00.588021", "updated_at": "2025-08-28T11:38:26.856573", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "77b47788-94f0-41d9-a89d-430df99428ed", "name": "Issue Review", "description": "Issue_Review", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/065ba63e-4ded-4298-9393-71cccf82eeb0.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/253aecdc-1609-45fc-ae1e-786fd3ebba5d.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1753864359635"}], "owner_id": "14902289-d9a4-43ec-8a31-989babf03fe3", "owner_name": "<PERSON><PERSON><PERSON><PERSON>", "use_count": 3, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-30T08:48:42.153426", "updated_at": "2025-08-22T04:50:43.419676", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "51729580-e6a7-42b8-843a-fbf0d4a71466", "name": "Google Sheets Row Range Reader v2", "description": "No description provided.", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/d4421b59-6265-4d06-8ef2-e6b48e280ed3.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/1f6e8cbd-f1d6-4244-a0d0-3c47874953dd.json", "start_nodes": null, "owner_id": "8da56b93-b442-4166-a8a1-b902bd38f363", "owner_name": "<PERSON><PERSON>", "use_count": 5, "execution_count": 0, "average_rating": null, "category": "general", "tags": null, "version": "1.0.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-30T06:25:56.314809", "updated_at": "2025-08-22T04:50:51.098979", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "b3ebf7a5-151c-4490-a56a-6b8b6f1ce148", "name": "Blog Generation v5.4", "description": "No description provided.", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/2d59af1e-5e34-4cf2-b464-11df5600404e.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/7ac7f1ee-6910-4ada-9e5e-bbceba726b2e.json", "start_nodes": null, "owner_id": "8da56b93-b442-4166-a8a1-b902bd38f363", "owner_name": "<PERSON><PERSON>", "use_count": 4, "execution_count": 0, "average_rating": null, "category": "general", "tags": null, "version": "1.0.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-30T05:44:56.280082", "updated_at": "2025-08-22T04:51:00.161482", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "1d0c7895-4304-4e49-9d6c-e1623588beea", "name": "Send Issue Review", "description": "Send_Issue_Review", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/556523b1-ce87-43cd-9aac-c1b9f21f30dc.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/889a45d5-1242-4ef9-814a-06d9d309d902.json", "start_nodes": [{"field": "issue<PERSON><PERSON>", "type": "string", "transition_id": "transition-MCP_<PERSON>ra_&_Confluence_get_issue-1753795019023"}], "owner_id": "14902289-d9a4-43ec-8a31-989babf03fe3", "owner_name": "<PERSON><PERSON><PERSON><PERSON>", "use_count": 4, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-29T13:23:35.141867", "updated_at": "2025-08-22T04:51:15.977717", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "3625d83b-0888-4016-85ca-dc23c964de58", "name": "Screening", "description": "Screening", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/16b34d58-34af-4acb-a07c-4ec95fc8ac29.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/c5e29efc-5738-4e4c-b84d-116456243e6d.json", "start_nodes": [{"field": "input_variables", "type": "dict", "transition_id": "transition-AgenticAI-1753435722849"}, {"field": "main_input", "type": "string", "transition_id": "transition-CombineTextComponent-1753780149499"}], "owner_id": "55b1d790-e922-40ec-9da9-7f784894ab42", "owner_name": "<PERSON><PERSON>", "use_count": 5, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.4.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-29T06:45:02.347839", "updated_at": "2025-09-01T10:09:41.877863", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "4c170d37-c427-4562-b2f2-400a61e67cf8", "name": "Blog Generation v5.3", "description": "Blog_Generation_v5.3", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/4fa22a17-2f4b-4b66-a361-291527da1e19.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/cfb97f1b-c02d-4a1d-9c4a-b663de82a1b2.json", "start_nodes": [{"field": "input_data", "type": "multiline", "transition_id": "transition-UniversalConverterComponent-1753068818880"}, {"field": "trigger", "type": "handle", "transition_id": "transition-IDGeneratorComponent-1753340852138"}, {"field": "trigger", "type": "handle", "transition_id": "transition-IDGeneratorComponent-1753341788889"}], "owner_id": "4f19b076-b555-4d69-ba27-59dc10ecbf58", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 3, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.2.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-25T12:43:36.144337", "updated_at": "2025-08-22T04:51:48.315592", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "80da0480-0dbc-40e0-a444-0bcfa78ba55b", "name": "Blog Generation v5.2", "description": "Blog_Generation_v5.2", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/e5c2156b-9bd8-4fff-a39f-3092910a912f.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/4529cbfe-2282-47e0-9e0d-8b9099acdeb1.json", "start_nodes": [{"field": "input_data", "type": "multiline", "transition_id": "transition-UniversalConverterComponent-1753068818880"}, {"field": "trigger", "type": "handle", "transition_id": "transition-IDGeneratorComponent-1753340852138"}, {"field": "trigger", "type": "handle", "transition_id": "transition-IDGeneratorComponent-1753341788889"}], "owner_id": "8da56b93-b442-4166-a8a1-b902bd38f363", "owner_name": "<PERSON><PERSON>", "use_count": 4, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.2.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-25T11:33:06.157465", "updated_at": "2025-08-22T04:52:18.825601", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "807c0057-3203-4199-af37-74e240547ada", "name": "Google Sheets Row Reader v1", "description": "Google_Sheets_Row_Reader_v1", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/78e3eb66-3da0-4af7-825f-3aa15706ddf0.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/a60f8f72-ab60-4c70-b51a-a9618afee843.json", "start_nodes": [{"field": "main_input", "type": "dict", "transition_id": "transition-MergeDataComponent-1753422353677"}], "owner_id": "4f19b076-b555-4d69-ba27-59dc10ecbf58", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 4, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-25T10:54:09.606373", "updated_at": "2025-08-22T04:52:12.049592", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "34a390ae-d969-43ea-a2e5-aad8e04e97a8", "name": "Google Sheets Row Range Reader v1", "description": "Google_Sheets_Row_Range_Reader_v1", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/149e5e76-0107-4872-bc5b-45182589e243.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/cca28a42-1118-4a84-84d0-3ef35d75fc25.json", "start_nodes": [{"field": "main_input", "type": "dict", "transition_id": "transition-MergeDataComponent-1753422353677"}], "owner_id": "4f19b076-b555-4d69-ba27-59dc10ecbf58", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 5, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-25T10:53:49.944171", "updated_at": "2025-08-22T04:52:44.647224", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "853393b7-5a81-4658-a992-c9478dc149d6", "name": "Blogs Finder V1", "description": "Blogs_Finder_V1", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/7d8ab514-6a56-4d99-aa0d-d287d68580bd.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/cecdaf3d-d173-43aa-be4b-fcb2060f0d9c.json", "start_nodes": [{"field": "main_input", "type": "dict", "transition_id": "transition-MergeDataComponent-1753422353677"}], "owner_id": "4f19b076-b555-4d69-ba27-59dc10ecbf58", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 5, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-25T09:51:57.052648", "updated_at": "2025-08-22T04:52:50.537357", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "03cbe420-1d29-4895-a775-b0cb5c0fb0aa", "name": "Blog Generation v5.1", "description": "Blog_Generation_v5.1", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/14d67a94-4a1c-4897-a1d9-bcd76924bc98.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/6deaa3cb-e993-4e83-b6de-5b36b4ed182e.json", "start_nodes": [{"field": "input_data", "type": "multiline", "transition_id": "transition-UniversalConverterComponent-1753068818880"}, {"field": "trigger", "type": "handle", "transition_id": "transition-IDGeneratorComponent-1753340852138"}, {"field": "trigger", "type": "handle", "transition_id": "transition-IDGeneratorComponent-1753341788889"}], "owner_id": "4f19b076-b555-4d69-ba27-59dc10ecbf58", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 4, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-24T12:17:30.945366", "updated_at": "2025-08-22T04:53:52.137711", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "7798ab31-fb91-49c1-b324-9c249994d059", "name": "Blog Generation v4 (Need Fix)", "description": "Blog_Generation_v4_(Need_Fix)", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/238ad630-6b64-44cf-bf6e-3e8edf13db29.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/f401ca25-9365-4ba2-92bd-d80155f4ef38.json", "start_nodes": [{"field": "input_data", "type": "multiline", "transition_id": "transition-UniversalConverterComponent-1753068818880"}], "owner_id": "4f19b076-b555-4d69-ba27-59dc10ecbf58", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 5, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.3.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-23T13:13:00.526585", "updated_at": "2025-08-22T04:54:05.943544", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "ea33e0c3-cf5e-40b3-8865-7b0dce77e045", "name": "Resume Scorer II", "description": "Resume_Scorer_II", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/627bc480-ff73-4ee7-8988-f96331fbaa7b.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/a7d6670e-1ab0-436c-a82c-6d195024cb47.json", "start_nodes": [{"field": "main_input", "type": "string", "transition_id": "transition-CombineTextComponent-1753416127009"}], "owner_id": "55b1d790-e922-40ec-9da9-7f784894ab42", "owner_name": "<PERSON><PERSON>", "use_count": 7, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.5.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-23T13:09:01.973921", "updated_at": "2025-09-01T13:17:55.709894", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "0143feae-68ee-4676-8729-1d4b71728dc7", "name": "Google Forms Responses To Sheets", "description": "Google_Forms_Responses_To_Sheets", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/89275ca0-9726-41c2-81fa-18eda7c566eb.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/5ab1a4a8-3bfb-40e3-8459-7dd0db83409d.json", "start_nodes": [{"field": "form_id", "type": "string", "transition_id": "transition-MCP_Google_Forms_get_google_form_responses-1753180185744"}, {"field": "title", "type": "string", "transition_id": "transition-MCP_Google_Sheets_create_worksheet-1753180229571"}, {"field": "worksheet_name", "type": "string", "transition_id": "transition-MCP_Google_Sheets_find_row-1753181883932"}], "owner_id": "598f7395-c39a-4bfa-9996-0e3bff89047c", "owner_name": "<PERSON><PERSON><PERSON><PERSON>", "use_count": 6, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-23T04:40:15.174550", "updated_at": "2025-08-22T04:54:34.980121", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "afb6aef3-9848-43fc-9ec5-755a045383e0", "name": "Blog Generation v3", "description": "Blog_Generation_v3", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/f0260007-c3e0-4ad8-86c5-e58922d9895d.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/eb1baa34-8afb-4b2a-9d80-0b8726903248.json", "start_nodes": [{"field": "input_data", "type": "multiline", "transition_id": "transition-UniversalConverterComponent-1753068818880"}], "owner_id": "4f19b076-b555-4d69-ba27-59dc10ecbf58", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 4, "execution_count": 0, "average_rating": null, "category": "general", "tags": null, "version": "1.0.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-22T06:27:54.087123", "updated_at": "2025-08-22T04:54:56.109165", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "1669c87f-2557-425f-a030-1c8f63f7796a", "name": "Clone of Blog Gen v2", "description": "Clone_of_Blog_Gen_v2", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/cd877384-7ad3-4d28-936a-d6195fd2dec8.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/2a25fff5-6074-4ba3-a4dd-0f9e68063cc0.json", "start_nodes": [{"field": "input_data", "type": "multiline", "transition_id": "transition-UniversalConverterComponent-1753068818880"}], "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 5, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-21T15:04:48.125776", "updated_at": "2025-08-22T04:55:28.458790", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "62c10ddb-2bf7-4da4-a8ab-81552dec4962", "name": "VC Automation - Research Workflow - Sheet Based", "description": "VC_Automation_-_Research_Workflow_-_Sheet_Based", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/3556c5e9-b116-4605-af0b-bf1dcdc0f46c.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/3a63f48c-208f-4e0e-bebf-02f93b31a5f2.json", "start_nodes": [{"field": "search_value", "type": "string", "transition_id": "transition-MCP_Google_Sheets_find_row-1752120450511"}], "owner_id": "12d153e7-9eb1-482b-85ba-d6c232e9f3c5", "owner_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "use_count": 4, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-21T08:40:50.442307", "updated_at": "2025-08-22T04:55:46.268077", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "51b51a93-e039-421d-8fd0-e4cbcb90e699", "name": "Blog Generation v2", "description": "Blog_Generation_v2", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/0860be82-9aea-49be-b184-b944e0a3e1cd.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/e6eca36b-98f3-40f3-9fa3-3e3dba3d2bb9.json", "start_nodes": [{"field": "input_data", "type": "multiline", "transition_id": "transition-UniversalConverterComponent-1753068818880"}], "owner_id": "8cd064a0-d28f-454b-8b65-6c66584ce827", "owner_name": "sajin bse", "use_count": 6, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.4.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-21T04:50:16.695243", "updated_at": "2025-08-22T04:55:58.578967", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "c75340d5-60d5-4e3a-93f0-b7d829693817", "name": "SpurRidge Workflow", "description": "SpurRidge_Workflow", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/8c8abc12-5a8e-4f1f-aad9-5dc53c43c257.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/a6d34cdb-a623-41d4-af1f-a1f9a1cd3051.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1752233311066"}], "owner_id": "d3b78018-fe5a-4dd4-8148-d533ab3d8a93", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 4, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-18T07:54:10.495147", "updated_at": "2025-08-22T04:56:08.312469", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "f59993f8-6f92-40b5-861c-410fa82c1114", "name": "Research Workflow", "description": "Research_Workflow", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/fbbf21e8-170c-4203-bfba-f37fec9882b8.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/bffc4b99-c7fe-42d6-a05e-477242b96e58.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1752769698362"}], "owner_id": "d3b78018-fe5a-4dd4-8148-d533ab3d8a93", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 4, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-18T07:54:01.436086", "updated_at": "2025-08-22T04:56:18.950204", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "b7a86377-dab3-49aa-a5d8-00f0ac29a81a", "name": "Profile Research Workflow", "description": "Profile_Research_Workflow", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/4a877ba7-1500-47fb-8eb2-acb80c5bccaf.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/4ed4f87c-1298-4b8c-823c-64f3fb3b8dbc.json", "start_nodes": [{"field": "iteration_list", "type": "list", "transition_id": "transition-LoopNode-1752762417794"}], "owner_id": "d3b78018-fe5a-4dd4-8148-d533ab3d8a93", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 6, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-18T07:53:54.317487", "updated_at": "2025-08-22T04:56:30.276593", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "4a1ba435-cb33-4e3b-9539-9b40ae30aca6", "name": "Company Research Workflow", "description": "Company_Research_Workflow", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/3d4f756a-ebdf-4e88-9c12-dc9185c8d94e.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/985e1f6f-22b5-4e4a-a44f-cb8df9424b5d.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1752768337944"}], "owner_id": "d3b78018-fe5a-4dd4-8148-d533ab3d8a93", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 4, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-18T07:53:40.739024", "updated_at": "2025-08-22T04:57:25.917929", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "fcfa7933-d5fb-4c9c-bcd0-a22254da694d", "name": "Meta ads Create", "description": "Meta_ads_Create", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/2179a182-9b13-4ea2-9555-7118ec6f35c1.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/0b74fb0b-18bc-46be-bd1b-8de866b1cee6.json", "start_nodes": [{"field": "name", "type": "string", "transition_id": "transition-MCP_MetaAds_create_meta_campaign-1752936013134"}, {"field": "objective", "type": "string", "transition_id": "transition-MCP_MetaAds_create_meta_campaign-1752936013134"}, {"field": "buying_type", "type": "string", "transition_id": "transition-MCP_MetaAds_create_meta_campaign-1752936013134"}, {"field": "daily_budget", "type": "int", "transition_id": "transition-MCP_MetaAds_create_meta_campaign-1752936013134"}, {"field": "name", "type": "string", "transition_id": "transition-MCP_MetaAds_create_adset-1753166235171"}, {"field": "daily_budget", "type": "int", "transition_id": "transition-MCP_MetaAds_create_adset-1753166235171"}, {"field": "countries", "type": "array", "transition_id": "transition-MCP_MetaAds_create_adset-1753166235171"}, {"field": "publisher_platforms", "type": "array", "transition_id": "transition-MCP_MetaAds_create_adset-1753166235171"}, {"field": "facebook_positions", "type": "array", "transition_id": "transition-MCP_MetaAds_create_adset-1753166235171"}, {"field": "optimization_goal", "type": "string", "transition_id": "transition-MCP_MetaAds_create_adset-1753166235171"}, {"field": "billing_event", "type": "string", "transition_id": "transition-MCP_MetaAds_create_adset-1753166235171"}, {"field": "bid_strategy", "type": "string", "transition_id": "transition-MCP_MetaAds_create_adset-1753166235171"}, {"field": "image_url", "type": "string", "transition_id": "transition-MCP_MetaAds_upload_ad_image-1753166529868"}, {"field": "name", "type": "string", "transition_id": "transition-MCP_MetaAds_create_ad_creative-1753166788555"}, {"field": "page_id", "type": "string", "transition_id": "transition-MCP_MetaAds_create_ad_creative-1753166788555"}, {"field": "link", "type": "string", "transition_id": "transition-MCP_MetaAds_create_ad_creative-1753166788555"}, {"field": "message", "type": "string", "transition_id": "transition-MCP_MetaAds_create_ad_creative-1753166788555"}, {"field": "image_name", "type": "string", "transition_id": "transition-MCP_MetaAds_create_ad_creative-1753166788555"}, {"field": "description", "type": "string", "transition_id": "transition-MCP_MetaAds_create_ad_creative-1753166788555"}, {"field": "call_to_action", "type": "object", "transition_id": "MCP_MetaAds_create_ad_creative-1753166788555", "properties": [{"field": "type", "type": "string", "transition_id": "MCP_MetaAds_create_ad_creative-1753166788555"}]}, {"field": "name", "type": "string", "transition_id": "transition-MCP_MetaAds_create_ad-1753330607929"}], "owner_id": "5229e05b-aaea-4850-9972-7268559318cf", "owner_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "use_count": 6, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.11.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-17T11:21:48.182521", "updated_at": "2025-08-22T05:00:04.887332", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "feb9555a-d851-4f0b-bbb5-bfef9f7bd0f6", "name": "Marketing-Performance-Report-Generators", "description": "Marketing-Performance-Report-Generators", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/ac9007dc-c243-4432-a032-f4e59072950e.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/c0907a23-4d2f-4896-bcad-a3b9d5dc82ff.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1752733978545"}, {"field": "input_variables", "type": "dict", "transition_id": "transition-AgenticAI-1752734214106"}], "owner_id": "2051be58-0123-407f-892f-cbe74966f0ab", "owner_name": "<PERSON><PERSON><PERSON> <PERSON>h", "use_count": 4, "execution_count": 0, "average_rating": null, "category": "automation", "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-17T06:56:44.218135", "updated_at": "2025-08-25T07:09:14.510888", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "ffa9a8d3-d833-4621-b580-983196cec30e", "name": "JD Creation", "description": "JD_Creation", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/73e0c513-e267-4ac4-8fb8-88da55e5a39b.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/b2e2814f-678a-4626-be4d-180453457783.json", "start_nodes": [{"field": "input_variables", "type": "dict", "transition_id": "transition-AgenticAI-1752566127607"}], "owner_id": "598f7395-c39a-4bfa-9996-0e3bff89047c", "owner_name": "<PERSON><PERSON><PERSON><PERSON>", "use_count": 8, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.16.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-16T06:08:50.360669", "updated_at": "2025-09-01T10:06:56.840519", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "4be3afad-64d2-4a93-bdf3-f708ae9a335d", "name": "VC Automation - Email Generator Workflow - Sheet Based", "description": "VC_Automation_-_Email_Generator_Workflow_-_Sheet_Based", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/690a6f0a-ea1b-4727-9760-3a561e301a6c.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/cb077574-c6d0-4f72-84bd-ef21aee556c7.json", "start_nodes": [{"field": "spreadsheet_id", "type": "string", "transition_id": "transition-MCP_Google_Sheets_find_row-1752247928534"}, {"field": "worksheet_name", "type": "string", "transition_id": "transition-MCP_Google_Sheets_get_cell-1752594639877"}], "owner_id": "12d153e7-9eb1-482b-85ba-d6c232e9f3c5", "owner_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "use_count": 9, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.3.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-14T05:36:01.607223", "updated_at": "2025-08-22T05:00:12.899771", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "4fdde5aa-6911-4bda-8123-f94e36e3afed", "name": "Website Generator", "description": "Website_Generator", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/3efeb724-2bef-4062-b54c-81077f7410e0.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/308ccf60-3ae0-4250-98a0-d80449b67ef4.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1751525393128"}], "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "owner_name": "<PERSON><PERSON>", "use_count": 21, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.2.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-03T13:34:26.316896", "updated_at": "2025-08-25T11:56:24.901012", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "18d5a6b9-c0e1-4e79-9300-daec6bf6e13a", "name": "Blog Gen 1", "description": "Blog_Gen_1", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/2a601a80-fe7d-4077-8c90-2fb877b80bf0.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/2f717d91-830f-47c1-ae72-f74411c9260b.json", "start_nodes": [{"field": "input_data", "type": "multiline", "transition_id": "transition-UniversalConverterComponent-1752749761139"}], "owner_id": "8da56b93-b442-4166-a8a1-b902bd38f363", "owner_name": "<PERSON><PERSON>", "use_count": 19, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.10.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-02T14:46:41.864509", "updated_at": "2025-08-22T05:00:37.027771", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "9ee4badd-3963-40b6-a59e-572f3c829980", "name": "PPT Generation", "description": "PPT_Generation", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/ae97cb34-979e-4c9b-9e2a-6d43c93d73ca.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/c2865dd2-829d-47a0-952f-27026dbce23f.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1751462692955"}], "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "owner_name": "<PERSON><PERSON>", "use_count": 17, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.2.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-01T14:14:34.537508", "updated_at": "2025-08-26T06:09:54.194157", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "3f014d21-f253-46bc-9a29-b87e6d30a967", "name": "Video Generation -<PERSON><PERSON><PERSON>", "description": "Video_Generation_-<PERSON><PERSON><PERSON>", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/29adbfb6-755b-49be-bf75-aa97d7053b38.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/465afdfd-0fee-4e68-be65-9c28d0501c4c.json", "start_nodes": [{"field": "topic", "type": "string", "transition_id": "transition-MCP_script-generation-mcp_script_generate-1752672788866"}], "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 22, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.3.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-06-27T14:10:21.608136", "updated_at": "2025-08-27T11:02:01.697840", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "32bc4198-825b-44b4-b43e-2b59c3d3e1dd", "name": "Ruh_Video_Generation", "description": "Ruh_Video_Generation", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/f7b13e6f-4bed-440d-be64-ebc04dc9431b.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/a5c22be3-a94e-4966-87f3-4e079e7c75a8.json", "start_nodes": [{"field": "script_type", "type": "string", "transition_id": "transition-MCP_script-generation-mcp-server_generate_script-1753182823017"}, {"field": "tone", "type": "string", "transition_id": "transition-MCP_script-generation-mcp-server_generate_script-1753182823017"}, {"field": "length", "type": "int", "transition_id": "transition-MCP_script-generation-mcp-server_generate_script-1753182823017"}, {"field": "topic", "type": "string", "transition_id": "transition-MCP_script-generation-mcp-server_generate_script-1753182823017"}, {"field": "characters", "type": "string", "transition_id": "transition-MCP_script-generation-mcp-server_generate_script-1753182823017"}, {"field": "scene", "type": "string", "transition_id": "transition-MCP_script-generation-mcp-server_generate_script-1753182823017"}, {"field": "cta", "type": "string", "transition_id": "transition-MCP_script-generation-mcp-server_generate_script-1753182823017"}, {"field": "view_type", "type": "dropdown", "transition_id": "transition-MCP_cinematic-video-generator_generate_video-1754056525924"}], "owner_id": "180c7469-1db0-4707-bc17-eeaa5e7ff64d", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 19, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.9.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-06-27T13:19:26.398759", "updated_at": "2025-08-22T05:01:51.151149", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "3cb41a82-1629-4082-8e09-e03e17424e22", "name": "Ciny_Video_generation", "description": "Ciny_Video_generation", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/dc3a85ee-efa7-4e01-bb02-766deb4a34cb.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/a0d69af5-1980-4783-a5bc-9a6a6468950a.json", "start_nodes": [{"field": "topic", "type": "string", "transition_id": "transition-MCP_Script_Generation_script_generate-1750321066832"}], "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 24, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.2.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-06-19T08:38:52.239472", "updated_at": "2025-08-22T05:01:59.102604", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "355a99de-e7ce-4741-bf29-bb1f37e52423", "name": "Agentic_component", "description": "Agentic_component", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/87eda95d-6e0d-4e79-b264-4e952652b982.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/8e7f9b24-c948-4e64-830e-116559e2827b.json", "start_nodes": [{"field": "main_input", "type": "string", "transition_id": "transition-CombineTextComponent-1750163464444"}, {"field": "jd_details", "type": "string", "transition_id": "transition-MCP_Candidate_Interview_generate_interview_agenda-1750170194752"}], "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 12, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-06-17T13:35:19.976078", "updated_at": "2025-08-22T05:02:03.615495", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "6ef72d4e-1dd6-4fa9-ac31-33ffe4a85909", "name": "Candidate_api_request", "description": "Candidate_api_request", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/79b4d632-a459-4d26-b1b4-7119318ead65.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/27710853-0dea-4fda-8fe3-5c499d111f84.json", "start_nodes": [{"field": "resume_s3_link", "type": "string", "transition_id": "transition-MCP_Candidate_Interview_candidate_suitability_pre-1750142649253"}, {"field": "job_description_s3_link", "type": "string", "transition_id": "transition-MCP_Candidate_Interview_candidate_suitability_pre-1750142649253"}], "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 19, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.2.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-06-17T07:13:32.854915", "updated_at": "2025-08-25T12:07:39.806120", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}, {"id": "b5551f78-e086-4ee2-aed5-92d504991724", "name": "script_audio_generation", "description": "script_audio_generation", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/d122768b-e844-4696-ac5d-042cdd368d93.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/32b8cd2e-eaac-465d-8bb0-aac1345ab407.json", "start_nodes": [{"field": "topic", "type": "string", "transition_id": "transition-MCP_Script_Generation_script_generate-1750143976588"}], "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 30, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.3.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-06-17T07:09:57.623074", "updated_at": "2025-08-25T20:53:51.204717", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": null, "source_workflow_id": null, "source_version_id": null}], "metadata": {"total": 50, "page": 1, "page_size": 100, "total_pages": 1, "has_next": false, "has_prev": false, "next_page": null, "prev_page": null}}