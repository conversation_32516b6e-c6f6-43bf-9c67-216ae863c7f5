{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "0931e5d9-fccc-459b-81a5-c1a251d16c7a", "name": "Google Document", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/Google-Docs-logo.png/1750839188-Google-Docs-logo.png", "description": "Google Document MCP Server.", "category": "general", "tags": null, "created_at": "2025-06-24T13:00:48.696889", "updated_at": "2025-08-15T08:17:26.874785", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "hosted_url": "https://google-docs-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["create_document", "update_document", "verify_connection", "append_document", "insert_image", "get_document"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "create_document", "description": "Create a new Google Document with optional title and content. Supports plain text, HTML, and Markdown formats.", "input_schema": {"properties": {"title": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Title"}, "content": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Content"}, "format": {"anyOf": [{"enum": ["plain", "html", "markdown"], "type": "string"}, {"type": "null"}], "default": "plain", "title": "Format"}}, "title": "CreateDocument", "type": "object"}, "output_schema": {"properties": {"upload document": {"type": "string", "description": "user uploads document to perform sentiment analysis", "title": "upload document"}}}, "annotations": null}, {"name": "update_document", "description": "Update a Google Document with new content at a specific position", "input_schema": {"properties": {"document_id": {"title": "Document Id", "type": "string"}, "content": {"title": "Content", "type": "string"}, "insert_at": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Insert At"}}, "required": ["document_id", "content"], "title": "UpdateDocument", "type": "object"}, "output_schema": {"properties": {"Update with sentiment analysis results": {"type": "string", "description": "updates users documents with sentiment analysis", "title": "Update with sentiment analysis results"}}}, "annotations": null}, {"name": "verify_connection", "description": "Verify the connection to Google Docs API and display user info", "input_schema": {"properties": {}, "title": "VerifyConnection", "type": "object"}, "output_schema": {"properties": {"jhvjh": {"type": "string", "description": "hbjbjhbhbj", "title": "jhvjh"}}}, "annotations": null}, {"name": "append_document", "description": "Append content to the end of a Google Document", "input_schema": {"properties": {"document_id": {"title": "Document Id", "type": "string"}, "content": {"title": "Content", "type": "string"}, "format": {"anyOf": [{"enum": ["plain", "html", "markdown"], "type": "string"}, {"type": "null"}], "default": "plain", "title": "Format"}}, "required": ["document_id", "content"], "title": "AppendDocument", "type": "object"}, "output_schema": {"properties": {"Upload Document": {"type": "string", "description": "user uploads their document", "title": "Upload Document"}}}, "annotations": null}, {"name": "insert_image", "description": "Insert an image into a Google Document at a specific position", "input_schema": {"properties": {"document_id": {"title": "Document Id", "type": "string"}, "image_url": {"title": "Image Url", "type": "string"}, "insert_index": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Insert Index"}, "width": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 300, "title": "<PERSON><PERSON><PERSON>"}, "height": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 200, "title": "Height"}, "alt_text": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Alt Text"}}, "required": ["document_id", "image_url"], "title": "InsertImage", "type": "object"}, "output_schema": {"properties": {"vjghvj": {"type": "string", "description": "jhhvhjv", "title": "vjghvj"}}}, "annotations": null}, {"name": "get_document", "description": "Retrieve the content of a Google Document by its ID", "input_schema": {"properties": {"document_id": {"title": "Document Id", "type": "string"}}, "required": ["document_id"], "title": "GetDocument", "type": "object"}, "output_schema": {}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": ["57e953e7-da31-4694-9e31-d623fcc65f2b"], "url": null}}