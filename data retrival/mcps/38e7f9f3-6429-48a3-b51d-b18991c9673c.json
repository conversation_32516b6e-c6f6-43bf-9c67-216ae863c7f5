{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "name": "<PERSON><PERSON><PERSON>", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/github.png/1751878385-github.png", "description": "The GitHub MCP Server is a Model Context Protocol (MCP) server that provides seamless integration with GitHub APIs, enabling advanced automation and interaction capabilities for developers and tools.", "category": "general", "tags": null, "created_at": "2025-07-07T08:53:28.891869", "updated_at": "2025-08-29T18:20:47.606362", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "hosted_url": "https://github-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["search_repositories", "get_repository", "create_repository", "fork_repository", "search_code", "get_file_contents", "search_users", "get_issue", "add_issue_comment", "search_issues", "create_issue", "list_issues", "update_issue", "get_issue_comments", "list_commits", "list_branches", "create_branch", "list_tags", "get_tag", "get_pull_request", "update_pull_request", "list_pull_requests", "merge_pull_request", "get_pull_request_files", "get_pull_request_status", "update_pull_request_branch", "get_pull_request_comments", "create_pull_request", "push_files", "get_commit", "create_or_update_file"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "search_repositories", "description": "Search for GitHub repositories. Use 'scope' parameter to control search scope: 'global' (default) searches all GitHub repositories, 'user' searches only within the authenticated user's repositories. Returns a concise list with essential information. Use 'get_repository' for detailed information about a specific repository.", "input_schema": {"properties": {"query": {"title": "Query", "type": "string"}, "sort": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Sort"}, "order": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Order"}, "per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 30, "title": "Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "scope": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "global", "title": "<PERSON><PERSON>"}}, "required": ["query"], "title": "SearchRepositories", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_repository", "description": "Get detailed information about a GitHub repository including README and file structure", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}}, "required": ["owner", "repo"], "title": "GetRepository", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_repository", "description": "Create a new GitHub repository in your account", "input_schema": {"properties": {"name": {"title": "Name", "type": "string"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Description"}, "private": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Private"}, "auto_init": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Auto Init"}}, "required": ["name"], "title": "CreateRepository", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "fork_repository", "description": "Fork a GitHub repository to your account or specified organization", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "organization": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Organization"}}, "required": ["owner", "repo"], "title": "ForkRepository", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "search_code", "description": "Search for code across GitHub repositories. Returns a concise list with file paths and repositories. Use 'get_file_contents' for full file content.", "input_schema": {"properties": {"query": {"title": "Query", "type": "string"}, "sort": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Sort"}, "order": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Order"}, "per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 30, "title": "Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}}, "required": ["query"], "title": "SearchCode", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_file_contents", "description": "Get the contents of a file from a GitHub repository", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "path": {"title": "Path", "type": "string"}, "ref": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Ref"}}, "required": ["owner", "repo", "path"], "title": "GetFileContents", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "search_users", "description": "Search for GitHub users", "input_schema": {"properties": {"query": {"title": "Query", "type": "string"}, "sort": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Sort"}, "order": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Order"}, "per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 30, "title": "Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}}, "required": ["query"], "title": "SearchUsers", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_issue", "description": "Get details of a specific issue in a GitHub repository", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "issue_number": {"title": "Issue Number", "type": "integer"}}, "required": ["owner", "repo", "issue_number"], "title": "GetIssue", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "add_issue_comment", "description": "Add a comment to a specific issue in a GitHub repository", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "issue_number": {"title": "Issue Number", "type": "integer"}, "body": {"title": "Body", "type": "string"}}, "required": ["owner", "repo", "issue_number", "body"], "title": "AddIssueComment", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "search_issues", "description": "Search for issues in GitHub repositories", "input_schema": {"properties": {"query": {"title": "Query", "type": "string"}, "sort": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Sort"}, "order": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Order"}, "per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 30, "title": "Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}}, "required": ["query"], "title": "SearchIssues", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_issue", "description": "Create a new issue in a GitHub repository", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "title": {"title": "Title", "type": "string"}, "body": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Body"}, "assignees": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Assignees"}, "labels": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Labels"}}, "required": ["owner", "repo", "title"], "title": "CreateIssue", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "list_issues", "description": "List issues in a GitHub repository", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "state": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "open", "title": "State"}, "sort": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "created", "title": "Sort"}, "direction": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "desc", "title": "Direction"}, "per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 30, "title": "Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}}, "required": ["owner", "repo"], "title": "ListIssues", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "update_issue", "description": "Update an existing issue in a GitHub repository", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "issue_number": {"title": "Issue Number", "type": "integer"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Title"}, "body": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Body"}, "state": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "State"}, "assignees": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Assignees"}, "labels": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Labels"}}, "required": ["owner", "repo", "issue_number"], "title": "UpdateIssue", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_issue_comments", "description": "Get comments for a specific issue in a GitHub repository", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "issue_number": {"title": "Issue Number", "type": "integer"}, "per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 30, "title": "Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}}, "required": ["owner", "repo", "issue_number"], "title": "GetIssueComments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "list_commits", "description": "Get list of commits of a branch in a GitHub repository", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "sha": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON>"}, "path": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Path"}, "author": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Author"}, "since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Since"}, "until": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Until"}, "per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 30, "title": "Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}}, "required": ["owner", "repo"], "title": "ListCommits", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "list_branches", "description": "List branches in a GitHub repository", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "protected": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Protected"}, "per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 30, "title": "Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}}, "required": ["owner", "repo"], "title": "ListBranches", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_branch", "description": "Create a new branch in a GitHub repository", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "branch": {"title": "Branch", "type": "string"}, "from_branch": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "main", "title": "From Branch"}}, "required": ["owner", "repo", "branch"], "title": "CreateBranch", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "list_tags", "description": "List git tags in a GitHub repository", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 30, "title": "Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}}, "required": ["owner", "repo"], "title": "ListTags", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_tag", "description": "Get details about a specific git tag in a GitHub repository", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "tag": {"title": "Tag", "type": "string"}}, "required": ["owner", "repo", "tag"], "title": "GetTag", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_pull_request", "description": "Get details of a specific pull request in a GitHub repository", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "pull_number": {"title": "Pull Number", "type": "integer"}}, "required": ["owner", "repo", "pull_number"], "title": "GetPullRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "update_pull_request", "description": "Update an existing pull request in a GitHub repository", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "pull_number": {"title": "Pull Number", "type": "integer"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Title"}, "body": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Body"}, "state": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "State"}, "base": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Base"}}, "required": ["owner", "repo", "pull_number"], "title": "UpdatePullRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "list_pull_requests", "description": "List pull requests in a GitHub repository", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "state": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "open", "title": "State"}, "head": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Head"}, "base": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Base"}, "sort": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "created", "title": "Sort"}, "direction": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "desc", "title": "Direction"}, "per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 30, "title": "Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}}, "required": ["owner", "repo"], "title": "ListPullRequests", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "merge_pull_request", "description": "Merge a pull request in a GitHub repository", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "pull_number": {"title": "Pull Number", "type": "integer"}, "commit_title": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Commit Title"}, "commit_message": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Commit Message"}, "merge_method": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "merge", "title": "Merge Method"}}, "required": ["owner", "repo", "pull_number"], "title": "MergePullRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_pull_request_files", "description": "Get the files changed in a specific pull request", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "pull_number": {"title": "Pull Number", "type": "integer"}, "per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 30, "title": "Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}}, "required": ["owner", "repo", "pull_number"], "title": "GetPullRequestFiles", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_pull_request_status", "description": "Get the status of a specific pull request", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "pull_number": {"title": "Pull Number", "type": "integer"}}, "required": ["owner", "repo", "pull_number"], "title": "GetPullRequestStatus", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "update_pull_request_branch", "description": "Update the branch of a pull request with the latest changes from the base branch (not implemented)", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "pull_number": {"title": "Pull Number", "type": "integer"}}, "required": ["owner", "repo", "pull_number"], "title": "UpdatePullRequestBranch", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_pull_request_comments", "description": "Get comments for a specific pull request", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "pull_number": {"title": "Pull Number", "type": "integer"}, "per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 30, "title": "Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}}, "required": ["owner", "repo", "pull_number"], "title": "GetPullRequestComments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_pull_request", "description": "Create a new pull request in a GitHub repository", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "title": {"title": "Title", "type": "string"}, "head": {"title": "Head", "type": "string"}, "base": {"title": "Base", "type": "string"}, "body": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Body"}, "draft": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Draft"}}, "required": ["owner", "repo", "title", "head", "base"], "title": "CreatePullRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "push_files", "description": "Push multiple files to a GitHub repository in a single commit", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "branch": {"title": "Branch", "type": "string"}, "message": {"title": "Message", "type": "string"}, "files": {"items": {"additionalProperties": true, "type": "object"}, "title": "Files", "type": "array"}}, "required": ["owner", "repo", "branch", "message", "files"], "title": "PushFiles", "type": "object"}, "output_schema": {"properties": {"project_name": {"type": "string", "description": "Name of Project repository", "title": "project_name"}, "result": {"type": "string", "description": "Changes committed and pushed successfully to repository", "title": "result"}}}, "annotations": null}, {"name": "get_commit", "description": "Get details for a commit from a GitHub repository", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "ref": {"title": "Ref", "type": "string"}}, "required": ["owner", "repo", "ref"], "title": "GetCommit", "type": "object"}, "output_schema": {"success": true, "data": {"sha": "5c94cf4a72060aaad6cc2d817432470972749e0c", "node_id": "C_kwDOO89b6toAKDVjOTRjZjRhNzIwNjBhYWFkNmNjMmQ4MTc0MzI0NzA5NzI3NDllMGM", "commit": {"author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "107841295+ab<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com", "date": "2025-07-10T10: 54: 02Z"}, "committer": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "107841295+ab<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com", "date": "2025-07-10T10: 54: 02Z"}, "message": "update test html file", "tree": {"sha": "1a674e7f5dbe32b1fac4546d048567e6a33d4892", "url": "https: //api.github.com/repos/abhishekrapid/sendgrid-mcp/git/trees/1a674e7f5dbe32b1fac4546d048567e6a33d4892"}, "url": "https: //api.github.com/repos/abhishekrapid/sendgrid-mcp/git/commits/5c94cf4a72060aaad6cc2d817432470972749e0c", "comment_count": 0, "verification": {"verified": false, "reason": "unsigned", "signature": null, "payload": null, "verified_at": null}}, "url": "https: //api.github.com/repos/abhishekrapid/sendgrid-mcp/commits/5c94cf4a72060aaad6cc2d817432470972749e0c", "html_url": "https: //github.com/abhishekrapid/sendgrid-mcp/commit/5c94cf4a72060aaad6cc2d817432470972749e0c", "comments_url": "https: //api.github.com/repos/abhishekrapid/sendgrid-mcp/commits/5c94cf4a72060aaad6cc2d817432470972749e0c/comments", "author": {"login": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "id": 107841295, "node_id": "U_kgDOBm2HDw", "avatar_url": "https: //avatars.githubusercontent.com/u/107841295?v=4", "gravatar_id": "", "url": "https: //api.github.com/users/abhishekrapid", "html_url": "https: //github.com/abhishekrapid", "followers_url": "https: //api.github.com/users/abhishekrapid/followers", "following_url": "https: //api.github.com/users/abhishekrapid/following{/other_user}", "gists_url": "https: //api.github.com/users/abhishekrapid/gists{/gist_id}", "starred_url": "https: //api.github.com/users/abhishe<PERSON><PERSON>/starred{/owner}{/repo}", "subscriptions_url": "https: //api.github.com/users/abhishekrapid/subscriptions", "organizations_url": "https: //api.github.com/users/abhishekrapid/orgs", "repos_url": "https: //api.github.com/users/abhishekrapid/repos", "events_url": "https: //api.github.com/users/abhishekrapid/events{/privacy}", "received_events_url": "https: //api.github.com/users/abhishekrapid/received_events", "type": "User", "user_view_type": "public", "site_admin": false}, "committer": {"login": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "id": 107841295, "node_id": "U_kgDOBm2HDw", "avatar_url": "https: //avatars.githubusercontent.com/u/107841295?v=4", "gravatar_id": "", "url": "https: //api.github.com/users/abhishekrapid", "html_url": "https: //github.com/abhishekrapid", "followers_url": "https: //api.github.com/users/abhishekrapid/followers", "following_url": "https: //api.github.com/users/abhishekrapid/following{/other_user}", "gists_url": "https: //api.github.com/users/abhishekrapid/gists{/gist_id}", "starred_url": "https: //api.github.com/users/abhishe<PERSON><PERSON>/starred{/owner}{/repo}", "subscriptions_url": "https: //api.github.com/users/abhishekrapid/subscriptions", "organizations_url": "https: //api.github.com/users/abhishekrapid/orgs", "repos_url": "https: //api.github.com/users/abhishekrapid/repos", "events_url": "https: //api.github.com/users/abhishekrapid/events{/privacy}", "received_events_url": "https: //api.github.com/users/abhishekrapid/received_events", "type": "User", "user_view_type": "public", "site_admin": false}, "parents": [{"sha": "65c1400d74c61a79e7d83fbb2491662a065f3315", "url": "https: //api.github.com/repos/abhishekrapid/sendgrid-mcp/commits/65c1400d74c61a79e7d83fbb2491662a065f3315", "html_url": "https: //github.com/abhishekrapid/sendgrid-mcp/commit/65c1400d74c61a79e7d83fbb2491662a065f3315"}], "stats": {"total": 2, "additions": 1, "deletions": 1}, "files": [{"sha": "67c421515f2fa40ece74d2037bf95203ad2d461f", "filename": "test.html", "status": "modified", "additions": 1, "deletions": 1, "changes": 2, "blob_url": "https: //github.com/abhishekrapid/sendgrid-mcp/blob/5c94cf4a72060aaad6cc2d817432470972749e0c/test.html", "raw_url": "https: //github.com/abhishekrapid/sendgrid-mcp/raw/5c94cf4a72060aaad6cc2d817432470972749e0c/test.html", "contents_url": "https: //api.github.com/repos/abhishekrapid/sendgrid-mcp/contents/test.html?ref=5c94cf4a72060aaad6cc2d817432470972749e0c", "patch": "@@ -1 +1 @@\n-<h1>Hello world </h1>\n\\ No newline at end of file\n+<h2>Hello World new </h2>\n\\ No newline at end of file"}]}, "status_code": 200}, "annotations": null}, {"name": "create_or_update_file", "description": "Create or update a single file in a GitHub repository. If updating an existing file, you must provide the current SHA of the file (the full 40-character SHA, not a shortened version).", "input_schema": {"properties": {"owner": {"title": "Owner", "type": "string"}, "repo": {"title": "Repo", "type": "string"}, "path": {"title": "Path", "type": "string"}, "message": {"title": "Message", "type": "string"}, "content": {"title": "Content", "type": "string"}, "sha": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON>"}, "branch": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Branch"}}, "required": ["owner", "repo", "path", "message", "content"], "title": "CreateOrUpdateFile", "type": "object"}, "output_schema": {"content": [{"type": "text", "text": {"success": true, "data": {"content": {"name": "test.html", "path": "test.html", "sha": "67c421515f2fa40ece74d2037bf95203ad2d461f", "size": 25, "url": "https://api.github.com/repos/abhishekrapid/sendgrid-mcp/contents/test.html?ref=main", "html_url": "https://github.com/abhishekrapid/sendgrid-mcp/blob/main/test.html", "git_url": "https://api.github.com/repos/abhishekrapid/sendgrid-mcp/git/blobs/67c421515f2fa40ece74d2037bf95203ad2d461f", "download_url": "https://raw.githubusercontent.com/abhishekrapid/sendgrid-mcp/main/test.html?token=AZWYOD2VVPYDYJDN36IDLCDIN6OPA", "type": "file", "_links": {"self": "https://api.github.com/repos/abhishekrapid/sendgrid-mcp/contents/test.html?ref=main", "git": "https://api.github.com/repos/abhishekrapid/sendgrid-mcp/git/blobs/67c421515f2fa40ece74d2037bf95203ad2d461f", "html": "https://github.com/abhishekrapid/sendgrid-mcp/blob/main/test.html"}}, "commit": {"sha": "9c6f9e68f328cb1243f7c0a88314b06bf5a26318", "node_id": "C_kwDOO89b6toAKDljNmY5ZTY4ZjMyOGNiMTI0M2Y3YzBhODgzMTRiMDZiZjVhMjYzMTg", "url": "https://api.github.com/repos/abhishekrapid/sendgrid-mcp/git/commits/9c6f9e68f328cb1243f7c0a88314b06bf5a26318", "html_url": "https://github.com/abhishekrapid/sendgrid-mcp/commit/9c6f9e68f328cb1243f7c0a88314b06bf5a26318", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "107841295+ab<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com", "date": "2025-07-10T10:57:56Z"}, "committer": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "107841295+ab<PERSON><PERSON><PERSON><PERSON>@users.noreply.github.com", "date": "2025-07-10T10:57:56Z"}, "tree": {"sha": "1a674e7f5dbe32b1fac4546d048567e6a33d4892", "url": "https://api.github.com/repos/abhishekrapid/sendgrid-mcp/git/trees/1a674e7f5dbe32b1fac4546d048567e6a33d4892"}, "message": "update test html file", "parents": [{"sha": "5c94cf4a72060aaad6cc2d817432470972749e0c", "url": "https://api.github.com/repos/abhishekrapid/sendgrid-mcp/git/commits/5c94cf4a72060aaad6cc2d817432470972749e0c", "html_url": "https://github.com/abhishekrapid/sendgrid-mcp/commit/5c94cf4a72060aaad6cc2d817432470972749e0c"}], "verification": {"verified": false, "reason": "unsigned", "signature": null, "payload": null, "verified_at": null}}}, "status_code": 200}}], "isError": false}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": ["c5e5b42b-3696-42ed-8c26-8aae3d16fffe"], "url": null}}