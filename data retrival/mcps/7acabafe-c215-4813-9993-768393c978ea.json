{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "7acabafe-c215-4813-9993-768393c978ea", "name": "Docker Sandbox", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/docker.png/1753353332-docker.png", "description": "The Docker Sandbox Creator is a powerful developer utility designed to spin up isolated coding environments inside Docker containers. Each sandbox operates as a lightweight, self-contained development workspace, perfect for safely testing code, running experiments, or building applications in any language or framework. With support for dynamic volume mounting, real-time file editing, and port exposure, this tool enables developers to rapidly prototype in clean, disposable environments—without polluting their host system.", "category": "engineering", "tags": ["Sandbox"], "created_at": "2025-07-18T16:43:05.313422", "updated_at": "2025-08-15T08:18:09.225601", "owner_id": "8510e2f9-15de-4024-9378-7f863a59cc6e", "hosted_url": "https://docker-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": "https://gitlab.rapidinnovation.tech/mcp-server/docker-mcp", "api_documentation": null, "capabilities": ["setup_container", "get_preview_url", "view_file_paths_in_container_dir", "add_dependency", "write", "view", "line_replace", "download_to_repo", "remove_dependency", "rename", "delete"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "setup_container", "description": "Set up a Docker container with the specified image name and tag.", "input_schema": {"description": "Schema for setting up a container environment.", "properties": {"application_type": {"default": "react", "description": "Type of application to set up (e.g., 'react', 'vue', 'angular', 'flask', 'fastapi', 'django', 'none')", "title": "Application Type", "type": "string"}, "image_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Docker image name to use (e.g., 'python', 'node')", "title": "Image Name"}, "image_tag": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Docker image tag to use (e.g., '3.12-slim', '20-alpine')", "title": "Image Tag"}, "command": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Custom command to run in the container (if not provided, a default command for the image will be used)", "title": "Command"}, "persist": {"default": true, "description": "Whether to persist the container after it exits", "title": "Persist", "type": "boolean"}}, "title": "SetupContainer", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_preview_url", "description": "Get preview url of provided container id", "input_schema": {"description": "Schema for getting a preview URL for a container.", "properties": {"container_id": {"description": "ID of the container to get the preview URL for", "title": "Container Id", "type": "string"}}, "required": ["container_id"], "title": "GetPreviewUrl", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "view_file_paths_in_container_dir", "description": "View all file paths in a Docker container's workspace.", "input_schema": {"description": "Schema for viewing file paths in a container.", "properties": {"container_id": {"description": "ID of the container to view file paths in", "title": "Container Id", "type": "string"}, "directory": {"default": "", "description": "Directory to list files in, relative to the workspace", "title": "Directory", "type": "string"}}, "required": ["container_id"], "title": "ViewFilePaths", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "add_dependency", "description": "Use this tool to add a dependency to the project. The dependency should be a valid npm package name.", "input_schema": {"description": "Schema for adding a dependency to the project.", "properties": {"container_id": {"description": "ID of the container to add the dependency to", "title": "Container Id", "type": "string"}, "package": {"description": "The dependency package name with optional version", "example": "lodash@latest", "title": "Package", "type": "string"}}, "required": ["container_id", "package"], "title": "AddDependency", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "write", "description": "Use this tool to write to a file. Overwrites the existing file if there is one. The file path should be relative to the project root.", "input_schema": {"description": "Schema for writing content to a file.", "properties": {"container_id": {"description": "ID of the container to write the file to", "title": "Container Id", "type": "string"}, "file_path": {"description": "The path of the file to write to", "example": "src/main.ts", "title": "File Path", "type": "string"}, "content": {"description": "The content to write to the file", "example": "console.log('Hello, <PERSON>!')", "title": "Content", "type": "string"}}, "required": ["container_id", "file_path", "content"], "title": "Write", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "view", "description": "Use this tool to read the contents of a file. The file path should be relative to the project root. You can optionally specify line ranges to read using the lines parameter.", "input_schema": {"description": "Schema for reading the contents of a file.", "properties": {"container_id": {"description": "ID of the container to read the file from", "title": "Container Id", "type": "string"}, "file_path": {"description": "The path of the file to read", "example": "src/App.tsx", "title": "File Path", "type": "string"}, "lines": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Optional line ranges to read", "example": "1-800, 1001-1500", "title": "Lines"}}, "required": ["container_id", "file_path"], "title": "View", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "line_replace", "description": "Line-Based Search and Replace Tool. Use this tool to find and replace specific content in a file using explicit line numbers.", "input_schema": {"description": "Schema for line-based search and replace in a file.", "properties": {"container_id": {"description": "ID of the container to modify the file in", "title": "Container Id", "type": "string"}, "file_path": {"description": "The path of the file to modify", "example": "src/components/TaskList.tsx", "title": "File Path", "type": "string"}, "search": {"description": "Content to search for in the file (without line numbers)", "example": " const handleTaskComplete = (taskId: string) => {\n setTasks(tasks.map(task =>\n...\n ));\n onTaskUpdate?.(updatedTasks);\n };", "title": "Search", "type": "string"}, "first_replaced_line": {"description": "First line number to replace (1-indexed)", "example": 15, "title": "First Replaced Line", "type": "integer"}, "last_replaced_line": {"description": "Last line number to replace (1-indexed)", "example": 28, "title": "Last Replaced Line", "type": "integer"}, "replace": {"description": "New content to replace the search content with", "example": " const handleTaskComplete = useCallback((taskId: string) => {\n const updatedTasks = tasks.map(task =>\n task.id === taskId \n ? { ...task, completed: !task.completed, completedAt: new Date() }\n : task\n );\n setTasks(updatedTasks);\n onTaskUpdate?.(updatedTasks);\n \n // Analytics tracking\n analytics.track('task_completed', { taskId, timestamp: Date.now() });\n }, [tasks, onTaskUpdate]);", "title": "Replace", "type": "string"}}, "required": ["container_id", "file_path", "search", "first_replaced_line", "last_replaced_line", "replace"], "title": "LineReplace", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "download_to_repo", "description": "Download a file from a URL and save it to the repository. Useful for downloading images, assets, or other files from URLs.", "input_schema": {"description": "Schema for downloading a file from a URL to the repository.", "properties": {"container_id": {"description": "ID of the container to download the file to", "title": "Container Id", "type": "string"}, "source_url": {"description": "The URL of the file to download", "example": "https://example.com/image.png", "title": "Source Url", "type": "string"}, "target_path": {"description": "The path where the file should be saved in the repository", "example": "public/images/logo.png", "title": "Target Path", "type": "string"}}, "required": ["container_id", "source_url", "target_path"], "title": "DownloadToRepo", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "remove_dependency", "description": "Use this tool to uninstall a package from the project.", "input_schema": {"description": "<PERSON><PERSON>a for removing a dependency from the project.", "properties": {"container_id": {"description": "ID of the container to remove the dependency from", "title": "Container Id", "type": "string"}, "package": {"description": "The package name to remove", "example": "lodash", "title": "Package", "type": "string"}}, "required": ["container_id", "package"], "title": "RemoveDependency", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "rename", "description": "You MUST use this tool to rename a file instead of creating new files and deleting old ones.", "input_schema": {"description": "<PERSON><PERSON>a for renaming a file.", "properties": {"container_id": {"description": "ID of the container to rename the file in", "title": "Container Id", "type": "string"}, "original_file_path": {"description": "The original path of the file", "example": "src/main.ts", "title": "Original File Path", "type": "string"}, "new_file_path": {"description": "The new path for the file", "example": "src/main_new2.ts", "title": "New File Path", "type": "string"}}, "required": ["container_id", "original_file_path", "new_file_path"], "title": "<PERSON><PERSON>", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "delete", "description": "Use this tool to delete a file. The file path should be relative to the project root.", "input_schema": {"description": "Schema for deleting a file.", "properties": {"container_id": {"description": "ID of the container to delete the file from", "title": "Container Id", "type": "string"}, "file_path": {"description": "The path of the file to delete", "example": "src/App.tsx", "title": "File Path", "type": "string"}}, "required": ["container_id", "file_path"], "title": "Delete", "type": "object"}, "output_schema": null, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": "docker-mcp", "git_user_name": "mcp-server", "integrations": null, "url": null}}