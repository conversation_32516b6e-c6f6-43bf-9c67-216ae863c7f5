{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "8bec32f3-5c9b-43ef-ae93-c0a46b7106ec", "name": "context-engine-mcp", "logo": null, "description": "fetch the context from the organisation data", "category": "general", "tags": null, "created_at": "2025-06-24T17:28:25.161680", "updated_at": "2025-08-14T05:16:29.692099", "owner_id": "180c7469-1db0-4707-bc17-eeaa5e7ff64d", "hosted_url": "https://context-engine-mcp-dev-624209391722.us-central1.run.app/mcp/", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["search", "upload_files_by_urls", "search_with_api_key", "upload_files_by_urls_with_api_key"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "search", "description": "Search for documents semantically similar to a query.", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "query_text": {"title": "Query Text", "type": "string"}, "organisation_id": {"title": "Organisation Id", "type": "string"}, "top_k": {"default": 10, "title": "Top K", "type": "integer"}, "agent_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Agent Id"}, "file_ids": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "File Ids"}, "least_score": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Least Score"}}, "required": ["user_id", "query_text", "organisation_id"], "type": "object"}, "output_schema": null, "annotations": null}, {"name": "upload_files_by_urls", "description": "Sync specific Google Drive files by URL.", "input_schema": {"properties": {"drive_url": {"items": {"type": "string"}, "title": "Drive Url", "type": "array"}, "agent_id": {"title": "Agent Id", "type": "string"}, "organisation_id": {"title": "Organisation Id", "type": "string"}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "User Id"}}, "required": ["drive_url", "agent_id", "organisation_id"], "type": "object"}, "output_schema": null, "annotations": null}, {"name": "search_with_api_key", "description": "Search for documents semantically similar to a query using API key authentication.\n\nArgs:\n    api_key (str): MANDATORY - Valid API key for authentication\n    query_text (str): The search query text\n    top_k (int): Number of results to return (default: 10)\n    agent_id (Optional[str]): Optional agent ID to filter results\n    file_ids (Optional[List[str]]): Optional list of specific file IDs to search within\n    least_score (Optional[float]): Optional minimum score threshold for results", "input_schema": {"properties": {"api_key": {"title": "Api Key", "type": "string"}, "query_text": {"title": "Query Text", "type": "string"}, "top_k": {"default": 10, "title": "Top K", "type": "integer"}, "agent_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Agent Id"}, "file_ids": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "File Ids"}, "least_score": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Least Score"}}, "required": ["api_key", "query_text"], "type": "object"}, "output_schema": null, "annotations": null}, {"name": "upload_files_by_urls_with_api_key", "description": "Sync specific Google Drive files by URL using API key authentication.\n\nArgs:\n    api_key (str): MANDATORY - Valid API key for authentication\n    drive_url (List[str]): List of Google Drive URLs to sync\n    agent_id (str): Agent ID for the sync operation", "input_schema": {"properties": {"api_key": {"title": "Api Key", "type": "string"}, "drive_url": {"items": {"type": "string"}, "title": "Drive Url", "type": "array"}, "agent_id": {"title": "Agent Id", "type": "string"}}, "required": ["api_key", "drive_url", "agent_id"], "type": "object"}, "output_schema": null, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}