{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "name": "MetaAds", "logo": null, "description": "MCP server acting as an interface to the Facebook Ads, enabling programmatic access to Facebook Ads data and management features.", "category": "marketing", "tags": ["ads"], "created_at": "2025-07-10T06:58:12.782791", "updated_at": "2025-08-15T08:29:31.789097", "owner_id": "5229e05b-aaea-4850-9972-7268559318cf", "hosted_url": "https://meta-mcp-dev-************.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["update_meta_campaign", "update_campaign_status", "get_campaign_details", "get_ad_accounts", "get_account_info", "get_account_pages", "get_campaigns", "get_campaign_details", "get_adsets", "get_adset_details", "create_adset", "update_adset", "get_ads", "get_ad_details", "update_ad", "get_ad_creatives", "upload_ad_image", "get_ad_image", "get_insights", "create_budget_schedule", "get_custom_audiences", "get_lookalike_audiences", "get_audience_insights", "get_campaign_insights", "get_ad_account_insights", "get_conversion_data", "get_pixel_events", "get_customer_segments", "get_audience_demographics", "gather_ad_requirements", "create_ad", "create_meta_campaign", "create_ad_creative"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "update_meta_campaign", "description": "Update an existing Meta Ads campaign with new configuration", "input_schema": {"$defs": {"MetaBidStrategy": {"description": "Meta Ads Bidding Strategies - Updated 2024+", "enum": ["LOWEST_COST_WITHOUT_CAP", "LOWEST_COST_WITH_BID_CAP", "LOWEST_COST_WITH_MIN_ROAS"], "title": "MetaBidStrategy", "type": "string"}, "MetaCampaignStatus": {"description": "Meta Ads Campaign Status", "enum": ["ACTIVE", "PAUSED", "DELETED", "ARCHIVED"], "title": "MetaCampaignStatus", "type": "string"}}, "description": "<PERSON><PERSON><PERSON> for updating a Meta Ads Campaign", "properties": {"campaign_id": {"description": "Campaign ID to update", "title": "Campaign Id", "type": "string"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "New campaign name", "title": "Name"}, "status": {"anyOf": [{"$ref": "#/$defs/MetaCampaignStatus"}, {"type": "null"}], "default": null, "description": "New campaign status"}, "daily_budget": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "New daily budget in cents", "title": "Daily Budget"}, "lifetime_budget": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "New lifetime budget in cents", "title": "Lifetime Budget"}, "start_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "New campaign start time (ISO format)", "title": "Start Time"}, "end_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "New campaign end time (ISO format)", "title": "End Time"}, "bid_strategy": {"anyOf": [{"$ref": "#/$defs/MetaBidStrategy"}, {"type": "null"}], "default": null, "description": "New bidding strategy"}, "special_ad_categories": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "description": "New special ad categories", "title": "Special Ad Categories"}}, "required": ["campaign_id"], "title": "MetaCampaignUpdate", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "update_campaign_status", "description": "Update the status of a Meta Ads campaign (ACTIVE, PAUSED, etc.)", "input_schema": {"$defs": {"MetaCampaignStatus": {"description": "Meta Ads Campaign Status", "enum": ["ACTIVE", "PAUSED", "DELETED", "ARCHIVED"], "title": "MetaCampaignStatus", "type": "string"}}, "description": "<PERSON><PERSON><PERSON> for updating campaign status only", "properties": {"campaign_id": {"description": "Campaign ID to update", "title": "Campaign Id", "type": "string"}, "status": {"$ref": "#/$defs/MetaCampaignStatus", "description": "New campaign status"}}, "required": ["campaign_id", "status"], "title": "MetaCampaignStatusUpdate", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_campaign_details", "description": "Get detailed information about a Meta Ads campaign", "input_schema": {"description": "<PERSON><PERSON><PERSON> for getting campaign details", "properties": {"campaign_id": {"description": "Campaign ID to retrieve", "title": "Campaign Id", "type": "string"}, "fields": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "description": "Specific fields to retrieve", "title": "Fields"}}, "required": ["campaign_id"], "title": "MetaCampaignGet", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_ad_accounts", "description": "Get ad accounts accessible by a user", "input_schema": {"description": "<PERSON><PERSON>a for getting ad accounts", "properties": {"user_id": {"default": "me", "description": "Meta user ID or 'me' for the current user", "title": "User Id", "type": "string"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 10, "description": "Maximum number of accounts to return", "title": "Limit"}}, "title": "GetAdAccountsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_account_info", "description": "Get detailed information about a specific ad account", "input_schema": {"description": "Schema for getting account info", "properties": {}, "title": "GetAccountInfoRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_account_pages", "description": "Get pages associated with a Meta Ads account", "input_schema": {"description": "Schema for getting account pages", "properties": {}, "title": "GetAccountPagesRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_campaigns", "description": "Get campaigns for a Meta Ads account with optional filtering", "input_schema": {"description": "<PERSON><PERSON><PERSON> for getting campaigns", "properties": {"limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 10, "description": "Maximum number of campaigns to return", "title": "Limit"}, "status_filter": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Filter by status (empty for all, or 'ACTIVE', 'PAUSED', etc.)", "title": "Status Filter"}}, "title": "GetCampaignsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_campaign_details", "description": "Get detailed information about a specific campaign", "input_schema": {"description": "Schema for getting campaign details (MCP version)", "properties": {"campaign_id": {"description": "Meta Ads campaign ID", "title": "Campaign Id", "type": "string"}}, "required": ["campaign_id"], "title": "McpMetaAdsCampaignDetailsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_adsets", "description": "Get ad sets for a Meta Ads account with optional filtering by campaign", "input_schema": {"description": "Sc<PERSON>a for getting ad sets", "properties": {"limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 10, "description": "Maximum number of ad sets to return", "title": "Limit"}, "campaign_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Optional campaign ID to filter by", "title": "Campaign Id"}}, "title": "GetAdSetsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_adset_details", "description": "Get detailed information about a specific ad set", "input_schema": {"description": "<PERSON><PERSON><PERSON> for getting ad set details", "properties": {"adset_id": {"description": "Meta Ads ad set ID", "title": "Adset Id", "type": "string"}}, "required": ["adset_id"], "title": "GetAdSetDetailsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_adset", "description": "Create a new ad set in a Meta Ads account", "input_schema": {"description": "<PERSON><PERSON><PERSON> for creating an ad set", "properties": {"campaign_id": {"description": "Meta Ads campaign ID this ad set belongs to", "title": "Campaign Id", "type": "string"}, "name": {"description": "Ad set name", "title": "Name", "type": "string"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "PAUSED", "description": "Initial ad set status", "title": "Status"}, "daily_budget": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Daily budget in cents", "title": "Daily Budget"}, "lifetime_budget": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Lifetime budget in cents", "title": "Lifetime Budget"}, "countries": {"description": "List of target countries (e.g., ['US', 'CA'])", "items": {}, "title": "Countries", "type": "array"}, "publisher_platforms": {"description": "List of publisher platforms (e.g., ['facebook', 'instagram'])", "items": {}, "title": "Publisher Platforms", "type": "array"}, "facebook_positions": {"description": "List of Facebook positions (e.g., ['feed', 'right_hand_column'])", "items": {}, "title": "Facebook Positions", "type": "array"}, "optimization_goal": {"description": "Conversion optimization goal (e.g., 'LINK_CLICKS')", "title": "Optimization Goal", "type": "string"}, "billing_event": {"description": "How you're charged (e.g., 'IMPRESSIONS')", "title": "Billing Event", "type": "string"}, "bid_amount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Bid amount in account currency (in cents)", "title": "<PERSON><PERSON>"}, "bid_strategy": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Bid strategy (e.g., 'LOWEST_COST')", "title": "Bid Strategy"}, "start_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Start time (ISO 8601)", "title": "Start Time"}, "end_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "End time (ISO 8601)", "title": "End Time"}}, "required": ["campaign_id", "name", "countries", "publisher_platforms", "facebook_positions", "optimization_goal", "billing_event"], "title": "CreateAdSetRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "update_adset", "description": "Update an ad set with new settings including frequency caps", "input_schema": {"description": "Schema for updating an ad set", "properties": {"adset_id": {"description": "Meta Ads ad set ID", "title": "Adset Id", "type": "string"}, "frequency_control_specs": {"anyOf": [{"items": {"additionalProperties": true, "type": "object"}, "type": "array"}, {"type": "null"}], "default": null, "description": "List of frequency control specifications", "title": "Frequency Control Specs"}, "bid_strategy": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Bid strategy (e.g., 'LOWEST_COST_WITH_BID_CAP')", "title": "Bid Strategy"}, "bid_amount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Bid amount in account currency (in cents for USD)", "title": "<PERSON><PERSON>"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Update ad set status (ACTIVE, PAUSED, etc.)", "title": "Status"}, "targeting": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "description": "Targeting specifications including targeting_automation", "title": "Targeting"}}, "required": ["adset_id"], "title": "UpdateAdSetRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_ads", "description": "Get ads for a Meta Ads account with optional filtering", "input_schema": {"description": "Schema for getting ads", "properties": {"limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 10, "description": "Maximum number of ads to return", "title": "Limit"}, "campaign_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Optional campaign ID to filter by", "title": "Campaign Id"}, "adset_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Optional ad set ID to filter by", "title": "Adset Id"}}, "title": "GetAdsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_ad_details", "description": "Get detailed information about a specific ad", "input_schema": {"description": "<PERSON><PERSON><PERSON> for getting ad details", "properties": {"ad_id": {"description": "Meta Ads ad ID", "title": "Ad Id", "type": "string"}}, "required": ["ad_id"], "title": "GetAdDetailsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "update_ad", "description": "Update an ad with new settings", "input_schema": {"description": "<PERSON><PERSON>a for updating an ad", "properties": {"ad_id": {"description": "Meta Ads ad ID", "title": "Ad Id", "type": "string"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Update ad status (ACTIVE, PAUSED, etc.)", "title": "Status"}, "bid_amount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Bid amount in account currency (in cents for USD)", "title": "<PERSON><PERSON>"}}, "required": ["ad_id"], "title": "UpdateAdRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_ad_creatives", "description": "Get creative details for a specific ad", "input_schema": {"description": "<PERSON><PERSON>a for getting ad creatives", "properties": {"ad_id": {"description": "Meta Ads ad ID", "title": "Ad Id", "type": "string"}}, "required": ["ad_id"], "title": "GetAdCreativesRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "upload_ad_image", "description": "Upload an image to use in Meta Ads creatives", "input_schema": {"description": "Schema for uploading an ad image", "properties": {"image_url": {"description": "Image file URL for upload", "title": "Image Url", "type": "string"}}, "required": ["image_url"], "title": "UploadAdImageRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_ad_image", "description": "Get, download, and visualize a Meta ad image in one step", "input_schema": {"description": "<PERSON><PERSON><PERSON> for getting an ad image", "properties": {"ad_id": {"description": "Meta Ads ad ID", "title": "Ad Id", "type": "string"}}, "required": ["ad_id"], "title": "GetAdImageRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_insights", "description": "Get performance insights for a campaign, ad set, ad or account", "input_schema": {"description": "<PERSON><PERSON>a for getting insights", "properties": {"object_id": {"description": "ID of the campaign, ad set, ad or account", "title": "Object Id", "type": "string"}, "time_range": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "maximum", "description": "Time range for insights", "title": "Time Range"}, "breakdown": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Optional breakdown dimension (e.g., age, gender, country)", "title": "Breakdown"}, "level": {"description": "Level of aggregation (ad, adset, campaign, account)", "title": "Level", "type": "string"}}, "required": ["object_id", "level"], "title": "GetInsightsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_budget_schedule", "description": "Create a budget schedule for a Meta Ads campaign", "input_schema": {"description": "Schema for creating a budget schedule", "properties": {"campaign_id": {"description": "Meta Ads campaign ID", "title": "Campaign Id", "type": "string"}, "budget_value": {"description": "Amount of budget increase", "title": "Budget Value", "type": "integer"}, "budget_value_type": {"description": "Type of budget value ('ABSOLUTE' or 'MULTIPLIER')", "title": "Budget Value Type", "type": "string"}, "time_start": {"description": "Unix timestamp for when the high demand period should start", "title": "Time Start", "type": "integer"}, "time_end": {"description": "Unix timestamp for when the high demand period should end", "title": "Time End", "type": "integer"}}, "required": ["campaign_id", "budget_value", "budget_value_type", "time_start", "time_end"], "title": "CreateBudgetScheduleRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_custom_audiences", "description": "Get custom audiences including email lists, purchase data, and website visitor audiences", "input_schema": {"description": "Schema for getting custom audiences", "properties": {"fields": {"description": "Fields to include in response", "items": {"type": "string"}, "title": "Fields", "type": "array"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 25, "description": "Maximum number of audiences to return", "title": "Limit"}}, "title": "CustomAudiencesRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_lookalike_audiences", "description": "Get lookalike audiences that are similar to your existing customers", "input_schema": {"description": "Schema for getting lookalike audiences", "properties": {"fields": {"description": "Fields to include in response", "items": {"type": "string"}, "title": "Fields", "type": "array"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 25, "description": "Maximum number of audiences to return", "title": "Limit"}}, "title": "LookalikeAudiencesRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_audience_insights", "description": "Get detailed insights and analytics for a specific audience including size estimates and demographics", "input_schema": {"description": "Schema for getting audience insights", "properties": {"audience_id": {"description": "Audience ID to get insights for", "title": "Audience Id", "type": "string"}, "fields": {"description": "Fields to include in response", "items": {"type": "string"}, "title": "Fields", "type": "array"}, "date_preset": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "last_30d", "description": "Date preset for insights (e.g., 'last_30d', 'last_7d', 'last_90d')", "title": "Date Preset"}}, "required": ["audience_id"], "title": "AudienceInsightsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_campaign_insights", "description": "Get historical campaign performance data including customer engagement metrics and conversion data", "input_schema": {"description": "Schema for getting campaign insights with customer data", "properties": {"campaign_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Specific campaign ID (optional)", "title": "Campaign Id"}, "fields": {"description": "Fields to include in response", "items": {"type": "string"}, "title": "Fields", "type": "array"}, "date_preset": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "last_30d", "description": "Date preset for insights", "title": "Date Preset"}, "time_range": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "default": null, "description": "Custom time range with 'since' and 'until' keys in YYYY-MM-DD format", "title": "Time Range"}, "level": {"default": "campaign", "description": "Level of insights (campaign, adset, ad)", "title": "Level", "type": "string"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 25, "description": "Maximum number of results to return", "title": "Limit"}}, "title": "CampaignInsightsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_ad_account_insights", "description": "Get comprehensive ad account insights including customer behavior and demographic breakdowns", "input_schema": {"description": "Sc<PERSON>a for getting ad account insights", "properties": {"fields": {"description": "Fields to include in response", "items": {"type": "string"}, "title": "Fields", "type": "array"}, "date_preset": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "last_30d", "description": "Date preset for insights", "title": "Date Preset"}, "time_range": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "default": null, "description": "Custom time range with 'since' and 'until' keys in YYYY-MM-DD format", "title": "Time Range"}, "breakdown": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "description": "Breakdown dimensions (e.g., ['age', 'gender', 'country'])", "title": "Breakdown"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 25, "description": "Maximum number of results to return", "title": "Limit"}}, "title": "AdAccountInsightsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_conversion_data", "description": "Get detailed conversion tracking data to understand customer actions and purchase behavior", "input_schema": {"description": "Schema for getting conversion data", "properties": {"fields": {"description": "Fields to include in response", "items": {"type": "string"}, "title": "Fields", "type": "array"}, "date_preset": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "last_30d", "description": "Date preset for conversion data", "title": "Date Preset"}, "time_range": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "default": null, "description": "Custom time range with 'since' and 'until' keys in YYYY-MM-DD format", "title": "Time Range"}, "action_breakdown": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "description": "Action breakdown types", "title": "Action Breakdown"}, "level": {"default": "account", "description": "Level of conversion data (account, campaign, adset, ad)", "title": "Level", "type": "string"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 25, "description": "Maximum number of results to return", "title": "Limit"}}, "title": "ConversionDataRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_pixel_events", "description": "Get Facebook Pixel events data to track customer interactions and website behavior", "input_schema": {"description": "Schema for getting pixel events data", "properties": {"pixel_id": {"description": "Facebook Pixel ID", "title": "Pixel Id", "type": "string"}, "fields": {"description": "Fields to include in response", "items": {"type": "string"}, "title": "Fields", "type": "array"}, "date_preset": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "last_30d", "description": "Date preset for pixel events", "title": "Date Preset"}, "time_range": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "default": null, "description": "Custom time range with 'since' and 'until' keys in YYYY-MM-DD format", "title": "Time Range"}, "breakdown": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "description": "Breakdown dimensions", "title": "Breakdown"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 25, "description": "Maximum number of results to return", "title": "Limit"}}, "required": ["pixel_id"], "title": "PixelEventsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_customer_segments", "description": "Get customer segments and audience categorization data for targeted marketing", "input_schema": {"description": "Schema for getting customer segments", "properties": {"fields": {"description": "Fields to include in response", "items": {"type": "string"}, "title": "Fields", "type": "array"}, "segment_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Type of segment to filter by", "title": "Segment Type"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 25, "description": "Maximum number of segments to return", "title": "Limit"}}, "title": "CustomerSegmentsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_audience_demographics", "description": "Get detailed demographic information about your audiences including age, gender, location, and interests", "input_schema": {"description": "Schema for getting audience demographics", "properties": {"audience_id": {"description": "Audience ID to get demographics for", "title": "Audience Id", "type": "string"}, "fields": {"description": "Demographic fields to include", "items": {"type": "string"}, "title": "Fields", "type": "array"}, "breakdown": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "description": "Breakdown dimensions for demographics", "title": "Breakdown"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 25, "description": "Maximum number of results to return", "title": "Limit"}}, "required": ["audience_id"], "title": "AudienceDemographicsRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "gather_ad_requirements", "description": "Comprehensive tool to gather all requirements for creating Meta Ads campaigns, ad sets, and creatives", "input_schema": {"$defs": {"MetaBidStrategy": {"description": "Meta Ads Bidding Strategies - Updated 2024+", "enum": ["LOWEST_COST_WITHOUT_CAP", "LOWEST_COST_WITH_BID_CAP", "LOWEST_COST_WITH_MIN_ROAS"], "title": "MetaBidStrategy", "type": "string"}, "MetaBillingEvent": {"description": "Meta Ads Billing Events", "enum": ["APP_INSTALLS", "CLICKS", "IMPRESSIONS", "LINK_CLICKS", "NONE", "OFFER_CLAIMS", "PAGE_LIKES", "POST_ENGAGEMENT", "THRUPLAY", "LANDING_PAGE_VIEWS", "PURCHASE", "LISTING_INTERACTION"], "title": "MetaBillingEvent", "type": "string"}, "MetaCallToActionType": {"description": "Meta Ads Call to Action Types", "enum": ["OPEN_LINK", "LIKE_PAGE", "SHOP_NOW", "PLAY_GAME", "INSTALL_APP", "USE_APP", "INSTALL_MOBILE_APP", "USE_MOBILE_APP", "BOOK_TRAVEL", "LISTEN_MUSIC", "WATCH_VIDEO", "LEARN_MORE", "SIGN_UP", "DOWNLOAD", "GET_QUOTE", "CONTACT_US", "APPLY_NOW", "BUY_NOW", "GET_OFFER", "GET_OFFER_VIEW", "BUY_TICKETS", "UPDATE_APP", "GET_DIRECTIONS", "BUY", "DONATE", "SUBSCRIBE", "SAY_THANKS", "SELL_NOW", "SHARE", "DONATE_NOW", "GET_STARTED", "VISIT_PAGES_FEED", "CALL_NOW", "EXPLORE_MORE", "CONFIRM", "JOIN_CHANNEL", "CALL", "REFER_FRIENDS", "MESSAGE_PAGE", "MOBILE_DOWNLOAD", "SAVE", "FOLLOW_PAGE", "WHATSAPP_MESSAGE", "FOLLOW_USER"], "title": "MetaCallToActionType", "type": "string"}, "MetaOptimizationGoal": {"description": "Meta Ads Optimization Goals", "enum": ["NONE", "APP_INSTALLS", "BRAND_AWARENESS", "CLICKS", "ENGAGED_USERS", "EVENT_RESPONSES", "IMPRESSIONS", "LEAD_GENERATION", "LINK_CLICKS", "OFFER_CLAIMS", "OFFSITE_CONVERSIONS", "PAGE_ENGAGEMENT", "PAGE_LIKES", "POST_ENGAGEMENT", "QUALITY_CALL", "REACH", "SOCIAL_IMPRESSIONS", "VIDEO_VIEWS", "THRUPLAY", "LANDING_PAGE_VIEWS", "VALUE", "CONVERSATIONS", "IN_APP_VALUE"], "title": "MetaOptimizationGoal", "type": "string"}}, "description": "Comprehensive schema for gathering ad creation requirements", "properties": {"name": {"description": "Name of the product or brand", "title": "Name", "type": "string"}, "objective": {"description": "Primary product or brand objective for the ad campaign", "title": "Objective", "type": "string"}, "daily_budget": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Daily budget in cents", "title": "Daily Budget"}, "geo_locations_countries": {"description": "List of countries to target", "items": {"type": "string"}, "title": "Geo Locations Countries", "type": "array"}, "publisher_platforms": {"description": "List of platforms to target", "items": {"type": "string"}, "title": "Publisher Platforms", "type": "array"}, "facebook_positions": {"description": "List of positions to target on Facebook", "items": {"type": "string"}, "title": "Facebook Positions", "type": "array"}, "optimization_goal": {"$ref": "#/$defs/MetaOptimizationGoal", "description": "What to optimize for"}, "billing_event": {"$ref": "#/$defs/MetaBillingEvent", "description": "What you're charged for"}, "bid_strategy": {"$ref": "#/$defs/MetaBidStrategy", "description": "Bidding strategy"}, "page_id": {"description": "Facebook page ID for the ad", "title": "Page Id", "type": "string"}, "ad_text_message": {"description": "Main text/message of the ad", "title": "Ad Text Message", "type": "string"}, "ad_headline": {"description": "Headline for the ad", "title": "Ad Headline", "type": "string"}, "ad_description": {"description": "Description text for the ad", "title": "Ad Description", "type": "string"}, "call_to_action": {"$ref": "#/$defs/MetaCallToActionType", "description": "Call to action button type"}, "landing_page_url": {"description": "URL where users will be directed", "title": "Landing Page Url", "type": "string"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "URL of the image to use", "title": "Image Url"}}, "required": ["name", "objective", "geo_locations_countries", "publisher_platforms", "facebook_positions", "optimization_goal", "billing_event", "bid_strategy", "page_id", "ad_text_message", "ad_headline", "ad_description", "call_to_action", "landing_page_url"], "title": "AdRequirementGatheringRequest", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_ad", "description": "Create a new ad with an existing creative", "input_schema": {"description": "<PERSON><PERSON><PERSON> for creating an ad", "example": {"adset_id": "<adset_id>", "creative_id": "<creative_id>", "name": "Ad 1", "status": "PAUSED"}, "properties": {"name": {"description": "Ad name", "title": "Name", "type": "string"}, "adset_id": {"description": "Ad set ID where this ad will be placed", "title": "Adset Id", "type": "string"}, "creative_id": {"description": "Creative ID to be used for this ad", "title": "Creative Id", "type": "string"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "PAUSED", "description": "Initial ad status", "title": "Status"}, "bid_amount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Optional bid amount (in cents)", "title": "<PERSON><PERSON>"}, "tracking_specs": {"anyOf": [{"items": {"additionalProperties": true, "type": "object"}, "type": "array"}, {"type": "null"}], "default": null, "description": "Optional tracking specifications", "title": "Tracking Specs"}}, "required": ["name", "adset_id", "creative_id"], "title": "CreateAdRequest", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "create_meta_campaign", "description": "Create a Meta Ads campaign with specified configuration", "input_schema": {"$defs": {"MetaBidStrategy": {"description": "Meta Ads Bidding Strategies - Updated 2024+", "enum": ["LOWEST_COST_WITHOUT_CAP", "LOWEST_COST_WITH_BID_CAP", "LOWEST_COST_WITH_MIN_ROAS"], "title": "MetaBidStrategy", "type": "string"}, "MetaCampaignObjective": {"description": "Meta Ads Campaign Objectives - Updated to match Meta API 2024+", "enum": ["OUTCOME_AWARENESS", "OUTCOME_ENGAGEMENT", "OUTCOME_LEADS", "OUTCOME_SALES", "OUTCOME_TRAFFIC", "OUTCOME_APP_PROMOTION", "BRAND_AWARENESS", "LINK_CLICKS", "POST_ENGAGEMENT", "LEAD_GENERATION", "APP_INSTALLS", "CONVERSIONS"], "title": "MetaCampaignObjective", "type": "string"}, "MetaCampaignStatus": {"description": "Meta Ads Campaign Status", "enum": ["ACTIVE", "PAUSED", "DELETED", "ARCHIVED"], "title": "MetaCampaignStatus", "type": "string"}}, "description": "Schema for creating a Meta Ads Campaign", "properties": {"name": {"description": "Campaign name", "title": "Name", "type": "string"}, "objective": {"$ref": "#/$defs/MetaCampaignObjective", "description": "Campaign objective"}, "status": {"$ref": "#/$defs/MetaCampaignStatus", "default": "PAUSED", "description": "Campaign status"}, "buying_type": {"default": "AUCTION", "description": "Buying type for the campaign", "title": "Buying Type", "type": "string"}, "bid_strategy": {"anyOf": [{"$ref": "#/$defs/MetaBidStrategy"}, {"type": "null"}], "default": null, "description": "Bidding strategy"}, "daily_budget": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Daily budget in cents", "title": "Daily Budget"}, "lifetime_budget": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Lifetime budget in cents", "title": "Lifetime Budget"}, "start_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Campaign start time (ISO format)", "title": "Start Time"}, "end_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Campaign end time (ISO format)", "title": "End Time"}, "special_ad_categories": {"description": "Special ad categories (required by Meta API)", "items": {"type": "string"}, "title": "Special Ad Categories", "type": "array"}}, "required": ["name", "objective"], "title": "MetaCampaignCreate", "type": "object"}, "output_schema": {"properties": {"vjhvvjv": {"type": "string", "description": "vghvvhv", "title": "vjhvvjv"}}}, "annotations": null}, {"name": "create_ad_creative", "description": "Create a new ad creative using an uploaded image hash", "input_schema": {"description": "<PERSON><PERSON><PERSON> for creating an ad creative", "example": {"call_to_action": {"type": "LEARN_MORE"}, "description": "Sleek. Powerful. Easy to Use.", "image_hash": "<uploaded_image_hash>", "link": "<landing-page-link>", "message": "This is the primary text for the ad. Discover why our new product is changing the game for everyone!", "name": "The Best New Product is Here!", "page_id": "<page_id>"}, "properties": {"name": {"description": "Creative name", "title": "Name", "type": "string"}, "page_id": {"description": "Page ID for the ad creative", "title": "Page Id", "type": "string"}, "image_hash": {"description": "Image hash for the ad creative", "title": "Image Hash", "type": "string"}, "link": {"description": "Landing page URL for the ad creative", "title": "Link", "type": "string"}, "message": {"description": "Primary text for the ad creative", "title": "Message", "type": "string"}, "image_name": {"description": "Name of the image", "title": "Image Name", "type": "string"}, "description": {"description": "Description of the ad creative", "title": "Description", "type": "string"}, "call_to_action": {"additionalProperties": true, "description": "Call to action for the ad creative", "title": "Call To Action", "type": "object"}}, "required": ["name", "page_id", "image_hash", "link", "message", "image_name", "description", "call_to_action"], "title": "CreateAdCreativeRequest", "type": "object"}, "output_schema": {"properties": {"nvvhnv": {"type": "string", "description": "hjbhjv", "title": "nvvhnv"}}}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}