{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "name": "SDR Management", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/icon.png/1756364539-icon.png", "description": "Sales Development Representative (SDR), capable of handling the end-to-end process of campaign creation, customer outreach, and performance tracking", "category": "sales", "tags": null, "created_at": "2025-08-11T14:52:36.797386", "updated_at": "2025-09-01T09:49:51.191694", "owner_id": "5229e05b-aaea-4850-9972-7268559318cf", "hosted_url": "https://sdr-management-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["Listing Products", "Creating a New Product", "Creating a New Campaign", "Fetching Campaign Details", "Fetching Campaigns", "Updating a Campaign", "Updating a Campaign Status", "Fetching Campaign Stats", "Fetching Active Campaigns", "Fetching Product Details", "Updating a Product", "Adding a Customer Meeting", "Starting Prospect Generation Workflow", "Listing Customers", "Listing Customers with Pagination", "Creating Customers", "Fetching a Customer", "Updating a Customer", "Fetching Customer Emails", "Finding Total number of propsects based on ICP", "Fetching User Settings", "Creating User Settings", "Updating User Settings", "Fetching Available Workflow Types", "Configuring User Workflows", "Creating a New Email Conversation", "Replying an Email Conversation from a Customer", "Fetching an Email Conversation", "Fetching Email Conversations", "Marking Email Conversations as Read", "Fetching Email Conversations with Pa<PERSON>ation", "Creating a New SMS Conversation", "Replying an SMS Conversation from a Customer", "Fetching an SMS Conversation", "Fetching SMS Conversations", "Marking SMS Conversations as Read", "Fetching SMS Conversations with Pagination", "Fetching Campaign Aggregated Conversations", "Fetching Customer Conversation Timeline", "Fetching Metrics"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "Listing Products", "description": "List products for a user (optionally filter by campaign_id)", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "campaign_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Campaign Id"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Limit"}, "offset": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Offset"}}, "required": ["user_id"], "title": "list_productsArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Creating a New Product", "description": "Create a product for a user and link it to a campaign", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "campaign_id": {"title": "Campaign Id", "type": "string"}, "name": {"title": "Name", "type": "string"}, "description": {"title": "Description", "type": "string"}, "features": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Features"}, "pricing": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Pricing"}, "website": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Website"}}, "required": ["user_id", "campaign_id", "name", "description"], "title": "create_productArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Creating a New Campaign", "description": "Create a new campaign", "input_schema": {"$defs": {"CampaignStyle": {"description": "Campaign intensity/style enumeration", "enum": ["aggressive", "moderate", "light"], "title": "CampaignStyle", "type": "string"}, "ChannelUse": {"description": "Channel modality enumeration for campaigns", "enum": ["email", "sms", "call", "multi_channel"], "title": "ChannelUse", "type": "string"}, "CompanySizeRange": {"enum": ["1_10", "11_50", "51_200", "201_500", "501_1000", "1000_plus"], "title": "CompanySizeRange", "type": "string"}, "DataSource": {"description": "Data source enumeration for campaigns", "enum": ["apollo", "zoominfo", "linkedin"], "title": "DataSource", "type": "string"}, "EmailTemplate": {"description": "Email template structure", "properties": {"subject": {"default": "", "description": "Email subject line", "title": "Subject", "type": "string"}, "template": {"default": "", "description": "Email template content", "title": "Template", "type": "string"}, "variables": {"description": "Template variables like {{first_name}}", "items": {"type": "string"}, "title": "Variables", "type": "array"}, "document": {"default": "", "description": "Document reference or content", "title": "Document", "type": "string"}}, "title": "EmailTemplate", "type": "object"}, "ICP": {"description": "Ideal Customer Profile (ICP) structure", "example": {"behavioral_signals": ["webinar_attendance", "ebook_downloads"], "budget_ranges": "$10k-$50k", "buyer_personas": ["Technical Decision Maker", "Ops Leader"], "company_size_range": ["1_10", "11_50"], "competitor_solutions": ["CompetitorA", "CompetitorB"], "industry": "SaaS", "intent_data_signals": ["website_visits", "job_postings"], "known_pain_points": ["Manual workflows", "Poor integrations"], "region": "United States", "revenue_range": "10m_50m", "target_role_title": "CTO", "technology_stack": ["Salesforce", "HubSpot"]}, "properties": {"industry": {"description": "Target industry/sector (e.g., SaaS, Healthcare, Retail)", "maxLength": 100, "title": "Industry", "type": "string"}, "region": {"description": "Geography/Region for prospects", "maxLength": 100, "title": "Region", "type": "string"}, "target_role_title": {"description": "Target role/title (e.g., CTO, Marketing Manager)", "maxLength": 200, "title": "Target Role Title", "type": "string"}, "keywords": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Keywords associated with target profile", "title": "Keywords"}, "person_titles": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Additional target person titles", "title": "Person Titles"}, "person_seniorities": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Target person seniority levels", "title": "Person Seniorities"}, "organization_locations": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Target organization locations", "title": "Organization Locations"}, "organization_domains_list": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Target organization domains", "title": "Organization Domains List"}, "company_size_range": {"anyOf": [{"items": {"$ref": "#/$defs/CompanySizeRange"}, "type": "array"}, {"type": "null"}], "default": null, "description": "List of Company size enum", "title": "Company Size Range"}, "revenue_range": {"anyOf": [{"$ref": "#/$defs/RevenueRange"}, {"type": "null"}], "default": null, "description": "Revenue range enum"}, "buyer_personas": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Buyer personas, max 5", "title": "Buyer Personas"}, "known_pain_points": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Known pain points, max 10", "title": "Known Pain Points"}, "technology_stack": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Technology stack, max 15", "title": "Technology Stack"}, "budget_ranges": {"anyOf": [{"maxLength": 200, "type": "string"}, {"type": "null"}], "default": null, "description": "Typical spending/deal sizes", "title": "Budget Ranges"}, "intent_data_signals": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Buying signals, max 8", "title": "Intent Data Signals"}, "competitor_solutions": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Competitor solutions in use, max 10", "title": "Competitor Solutions"}, "behavioral_signals": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Behavioral signals, max 8", "title": "Behavioral Signals"}}, "required": ["industry", "region", "target_role_title"], "title": "ICP", "type": "object"}, "LinkedInTemplate": {"description": "LinkedIn template structure", "properties": {"template": {"default": "", "description": "LinkedIn message template", "title": "Template", "type": "string"}, "document": {"default": "", "description": "Document reference or content", "title": "Document", "type": "string"}}, "title": "LinkedInTemplate", "type": "object"}, "RevenueRange": {"enum": ["under_1m", "1m_10m", "10m_50m", "50m_100m", "100m_plus"], "title": "RevenueRange", "type": "string"}, "Step": {"description": "Step in campaign sequence", "properties": {"step_type": {"$ref": "#/$defs/StepType", "description": "Step type: email, sms, or call"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Step title, e.g., Initial Email, Follow-up Email", "title": "Title"}, "offset_value": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Offset value from campaign start", "title": "Offset Value"}, "offset_unit": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Offset unit: minutes, hours, or days", "title": "Offset Unit"}, "task_note": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "", "description": "Optional task note for this step", "title": "Task Note"}, "scheduled_datetime": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "default": null, "description": "Calculated scheduled datetime for this step", "title": "Scheduled Datetime"}, "scheduler_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "ID of the scheduler for this step", "title": "Scheduler Id"}, "workflow_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "ID of the workflow this step belongs to", "title": "Workflow Id"}, "workflow_admin_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Admin workflow ID for this step", "title": "Workflow Admin Id"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "description": "Whether this step is currently active", "title": "Is Active"}}, "required": ["step_type"], "title": "Step", "type": "object"}, "StepType": {"description": "Step type enumeration", "enum": ["email", "sms", "call"], "title": "StepType", "type": "string"}}, "properties": {"user_id": {"title": "User Id", "type": "string"}, "name": {"title": "Name", "type": "string"}, "description": {"title": "Description", "type": "string"}, "conversation_id": {"title": "Conversation Id", "type": "string"}, "primary_value_proposition": {"title": "Primary Value Proposition", "type": "string"}, "target_outcome": {"title": "Target Outcome", "type": "string"}, "campaign_type": {"$ref": "#/$defs/CampaignStyle"}, "channels_to_use": {"default": null, "items": {"$ref": "#/$defs/ChannelUse"}, "title": "Channels To Use", "type": "array"}, "scheduled_start": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Scheduled Start"}, "data_source": {"anyOf": [{"items": {"$ref": "#/$defs/DataSource"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Data Source"}, "email_template": {"anyOf": [{"$ref": "#/$defs/EmailTemplate"}, {"type": "null"}], "default": null}, "linkedin_template": {"anyOf": [{"$ref": "#/$defs/LinkedInTemplate"}, {"type": "null"}], "default": null}, "target_audience": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Target Audience"}, "total_target_prospects": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Total Target Prospects"}, "sequence_steps": {"anyOf": [{"items": {"$ref": "#/$defs/Step"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Sequence Steps"}, "icp": {"anyOf": [{"$ref": "#/$defs/ICP"}, {"type": "null"}], "default": null}, "core_features_capabilities": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Core Features Capabilities"}, "differentiators": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Differentiators"}, "pricing_tiers_offers": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Pricing Tiers Offers"}, "customer_case_studies": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Customer Case Studies"}, "testimonials_reviews": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Testimonials Reviews"}, "supporting_assets": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Supporting Assets"}, "current_customers_logos": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Current Customers Logos"}, "number_of_touchpoints": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Number Of Touchpoints"}, "timing_preferences": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Timing Preferences"}, "preferred_days_times": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Preferred Days Times"}, "message_tone": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Message Tone"}, "content_priorities": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Content Priorities"}, "exit_criteria": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Exit Criteria"}, "follow_up_routing": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Follow Up Routing"}, "personalization_depth": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Personalization Depth"}}, "required": ["user_id", "name", "description", "conversation_id", "primary_value_proposition", "target_outcome", "campaign_type"], "title": "create_campaignArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Fetching Campaign Details", "description": "Get campaign details by conversation ID or campaign ID (at least one must be provided)", "input_schema": {"properties": {"conversation_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Conversation Id"}, "campaign_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Campaign Id"}, "user_id": {"default": null, "title": "User Id", "type": "string"}}, "title": "get_campaignArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Fetching Campaigns", "description": "Get list of campaigns for user with optional filtering", "input_schema": {"$defs": {"CampaignStatus": {"description": "Campaign status enumeration", "enum": ["draft", "active", "paused", "completed", "archived"], "title": "CampaignStatus", "type": "string"}}, "properties": {"user_id": {"title": "User Id", "type": "string"}, "status": {"anyOf": [{"$ref": "#/$defs/CampaignStatus"}, {"type": "null"}], "default": null}, "conversation_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Conversation Id"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Limit"}, "offset": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Offset"}}, "required": ["user_id"], "title": "get_campaignsArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Updating a Campaign", "description": "Update campaign details", "input_schema": {"$defs": {"CampaignStyle": {"description": "Campaign intensity/style enumeration", "enum": ["aggressive", "moderate", "light"], "title": "CampaignStyle", "type": "string"}, "ChannelUse": {"description": "Channel modality enumeration for campaigns", "enum": ["email", "sms", "call", "multi_channel"], "title": "ChannelUse", "type": "string"}, "CompanySizeRange": {"enum": ["1_10", "11_50", "51_200", "201_500", "501_1000", "1000_plus"], "title": "CompanySizeRange", "type": "string"}, "DataSource": {"description": "Data source enumeration for campaigns", "enum": ["apollo", "zoominfo", "linkedin"], "title": "DataSource", "type": "string"}, "EmailTemplate": {"description": "Email template structure", "properties": {"subject": {"default": "", "description": "Email subject line", "title": "Subject", "type": "string"}, "template": {"default": "", "description": "Email template content", "title": "Template", "type": "string"}, "variables": {"description": "Template variables like {{first_name}}", "items": {"type": "string"}, "title": "Variables", "type": "array"}, "document": {"default": "", "description": "Document reference or content", "title": "Document", "type": "string"}}, "title": "EmailTemplate", "type": "object"}, "ICP": {"description": "Ideal Customer Profile (ICP) structure", "example": {"behavioral_signals": ["webinar_attendance", "ebook_downloads"], "budget_ranges": "$10k-$50k", "buyer_personas": ["Technical Decision Maker", "Ops Leader"], "company_size_range": ["1_10", "11_50"], "competitor_solutions": ["CompetitorA", "CompetitorB"], "industry": "SaaS", "intent_data_signals": ["website_visits", "job_postings"], "known_pain_points": ["Manual workflows", "Poor integrations"], "region": "United States", "revenue_range": "10m_50m", "target_role_title": "CTO", "technology_stack": ["Salesforce", "HubSpot"]}, "properties": {"industry": {"description": "Target industry/sector (e.g., SaaS, Healthcare, Retail)", "maxLength": 100, "title": "Industry", "type": "string"}, "region": {"description": "Geography/Region for prospects", "maxLength": 100, "title": "Region", "type": "string"}, "target_role_title": {"description": "Target role/title (e.g., CTO, Marketing Manager)", "maxLength": 200, "title": "Target Role Title", "type": "string"}, "keywords": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Keywords associated with target profile", "title": "Keywords"}, "person_titles": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Additional target person titles", "title": "Person Titles"}, "person_seniorities": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Target person seniority levels", "title": "Person Seniorities"}, "organization_locations": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Target organization locations", "title": "Organization Locations"}, "organization_domains_list": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Target organization domains", "title": "Organization Domains List"}, "company_size_range": {"anyOf": [{"items": {"$ref": "#/$defs/CompanySizeRange"}, "type": "array"}, {"type": "null"}], "default": null, "description": "List of Company size enum", "title": "Company Size Range"}, "revenue_range": {"anyOf": [{"$ref": "#/$defs/RevenueRange"}, {"type": "null"}], "default": null, "description": "Revenue range enum"}, "buyer_personas": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Buyer personas, max 5", "title": "Buyer Personas"}, "known_pain_points": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Known pain points, max 10", "title": "Known Pain Points"}, "technology_stack": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Technology stack, max 15", "title": "Technology Stack"}, "budget_ranges": {"anyOf": [{"maxLength": 200, "type": "string"}, {"type": "null"}], "default": null, "description": "Typical spending/deal sizes", "title": "Budget Ranges"}, "intent_data_signals": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Buying signals, max 8", "title": "Intent Data Signals"}, "competitor_solutions": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Competitor solutions in use, max 10", "title": "Competitor Solutions"}, "behavioral_signals": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Behavioral signals, max 8", "title": "Behavioral Signals"}}, "required": ["industry", "region", "target_role_title"], "title": "ICP", "type": "object"}, "LinkedInTemplate": {"description": "LinkedIn template structure", "properties": {"template": {"default": "", "description": "LinkedIn message template", "title": "Template", "type": "string"}, "document": {"default": "", "description": "Document reference or content", "title": "Document", "type": "string"}}, "title": "LinkedInTemplate", "type": "object"}, "RevenueRange": {"enum": ["under_1m", "1m_10m", "10m_50m", "50m_100m", "100m_plus"], "title": "RevenueRange", "type": "string"}, "Step": {"description": "Step in campaign sequence", "properties": {"step_type": {"$ref": "#/$defs/StepType", "description": "Step type: email, sms, or call"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Step title, e.g., Initial Email, Follow-up Email", "title": "Title"}, "offset_value": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "description": "Offset value from campaign start", "title": "Offset Value"}, "offset_unit": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Offset unit: minutes, hours, or days", "title": "Offset Unit"}, "task_note": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "", "description": "Optional task note for this step", "title": "Task Note"}, "scheduled_datetime": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "default": null, "description": "Calculated scheduled datetime for this step", "title": "Scheduled Datetime"}, "scheduler_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "ID of the scheduler for this step", "title": "Scheduler Id"}, "workflow_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "ID of the workflow this step belongs to", "title": "Workflow Id"}, "workflow_admin_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Admin workflow ID for this step", "title": "Workflow Admin Id"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "description": "Whether this step is currently active", "title": "Is Active"}}, "required": ["step_type"], "title": "Step", "type": "object"}, "StepType": {"description": "Step type enumeration", "enum": ["email", "sms", "call"], "title": "StepType", "type": "string"}}, "properties": {"conversation_id": {"title": "Conversation Id", "type": "string"}, "user_id": {"title": "User Id", "type": "string"}, "campaign_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Campaign Id"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Description"}, "primary_value_proposition": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Primary Value Proposition"}, "target_outcome": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Target Outcome"}, "campaign_type": {"anyOf": [{"$ref": "#/$defs/CampaignStyle"}, {"type": "null"}], "default": null}, "channels_to_use": {"anyOf": [{"items": {"$ref": "#/$defs/ChannelUse"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Channels To Use"}, "scheduled_start": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Scheduled Start"}, "data_source": {"anyOf": [{"items": {"$ref": "#/$defs/DataSource"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Data Source"}, "email_template": {"anyOf": [{"$ref": "#/$defs/EmailTemplate"}, {"type": "null"}], "default": null}, "linkedin_template": {"anyOf": [{"$ref": "#/$defs/LinkedInTemplate"}, {"type": "null"}], "default": null}, "target_audience": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Target Audience"}, "total_target_prospects": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Total Target Prospects"}, "sequence_steps": {"anyOf": [{"items": {"$ref": "#/$defs/Step"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Sequence Steps"}, "icp": {"anyOf": [{"$ref": "#/$defs/ICP"}, {"type": "null"}], "default": null}, "core_features_capabilities": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Core Features Capabilities"}, "differentiators": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Differentiators"}, "pricing_tiers_offers": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Pricing Tiers Offers"}, "customer_case_studies": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Customer Case Studies"}, "testimonials_reviews": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Testimonials Reviews"}, "supporting_assets": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Supporting Assets"}, "current_customers_logos": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Current Customers Logos"}, "number_of_touchpoints": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Number Of Touchpoints"}, "timing_preferences": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Timing Preferences"}, "preferred_days_times": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Preferred Days Times"}, "message_tone": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Message Tone"}, "content_priorities": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Content Priorities"}, "exit_criteria": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Exit Criteria"}, "follow_up_routing": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Follow Up Routing"}, "personalization_depth": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Personalization Depth"}}, "required": ["conversation_id", "user_id"], "title": "update_campaignArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Updating a Campaign Status", "description": "Update campaign status", "input_schema": {"$defs": {"CampaignStatus": {"description": "Campaign status enumeration", "enum": ["draft", "active", "paused", "completed", "archived"], "title": "CampaignStatus", "type": "string"}}, "properties": {"conversation_id": {"title": "Conversation Id", "type": "string"}, "user_id": {"title": "User Id", "type": "string"}, "status": {"$ref": "#/$defs/CampaignStatus"}}, "required": ["conversation_id", "user_id", "status"], "title": "update_campaign_statusArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Fetching Campaign Stats", "description": "Get campaign statistics and performance metrics", "input_schema": {"properties": {"campaign_id": {"title": "Campaign Id", "type": "string"}, "user_id": {"title": "User Id", "type": "string"}}, "required": ["campaign_id", "user_id"], "title": "get_campaign_statsArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Fetching Active Campaigns", "description": "Get active campaigns for user", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}}, "required": ["user_id"], "title": "get_active_campaignsArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Fetching Product Details", "description": "Fetch product details by product_id and user_id", "input_schema": {"properties": {"product_id": {"title": "Product Id", "type": "string"}, "user_id": {"title": "User Id", "type": "string"}}, "required": ["product_id", "user_id"], "title": "fetch_productArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Updating a Product", "description": "Update product by product_id and user_id", "input_schema": {"properties": {"product_id": {"title": "Product Id", "type": "string"}, "user_id": {"title": "User Id", "type": "string"}, "update": {"additionalProperties": true, "title": "Update", "type": "object"}}, "required": ["product_id", "user_id", "update"], "title": "update_productArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Adding a Customer Meeting", "description": "Add a customer meeting", "input_schema": {"properties": {"customer_id": {"title": "Customer Id", "type": "string"}, "user_id": {"title": "User Id", "type": "string"}, "campaign_id": {"title": "Campaign Id", "type": "string"}}, "required": ["customer_id", "user_id", "campaign_id"], "title": "add_customer_meetingArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Starting Prospect Generation Workflow", "description": "Start prospect generation workflow for a campaign", "input_schema": {"properties": {"conversation_id": {"title": "Conversation Id", "type": "string"}, "user_id": {"title": "User Id", "type": "string"}}, "required": ["conversation_id", "user_id"], "title": "start_prospect_generation_workflowArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Listing Customers", "description": "List customers for a user (optionally filter by campaign_id and communication status)", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "campaign_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Campaign Id"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Limit"}, "offset": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Offset"}, "is_email_sent": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "<PERSON>"}, "is_email_replied": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Is Email Replied"}, "is_sms_sent": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Is Sms Sent"}, "is_sms_replied": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Is Sms Replied"}}, "required": ["user_id"], "title": "list_customersArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Listing Customers with Pagination", "description": "List customers for a user (optionally filter by campaign_id and communication status) with pagination", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "campaign_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Campaign Id"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Limit"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Page"}, "is_email_sent": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "<PERSON>"}, "is_email_replied": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Is Email Replied"}, "is_sms_sent": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Is Sms Sent"}, "is_sms_replied": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Is Sms Replied"}}, "required": ["user_id"], "title": "list_customers_with_pageArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Creating Customers", "description": "Bulk create customers for a campaign from a list of Apollo person objects", "input_schema": {"properties": {"customers": {"items": {"additionalProperties": true, "type": "object"}, "title": "Customers", "type": "array"}, "user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "User Id"}, "campaign_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Campaign Id"}, "tags": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Tags"}}, "required": ["customers"], "title": "create_customersArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Fetching a Customer", "description": "Fetch customer details by customer_id and user_id", "input_schema": {"properties": {"customer_id": {"title": "Customer Id", "type": "string"}, "user_id": {"title": "User Id", "type": "string"}}, "required": ["customer_id", "user_id"], "title": "fetch_customerArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Updating a Customer", "description": "Update customer persona, action_item, relevant_score, and classification fields", "input_schema": {"properties": {"customer_id": {"title": "Customer Id", "type": "string"}, "user_id": {"title": "User Id", "type": "string"}, "persona": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON>a"}, "action_item": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "Action Item"}, "relevant_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Relevant Score"}, "classification": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Classification"}}, "required": ["customer_id", "user_id"], "title": "update_customerArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Fetching Customer Emails", "description": "Fetch only customer email addresses for a user (optionally filter by campaign_id and communication status)", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "campaign_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Campaign Id"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Limit"}, "offset": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Offset"}, "is_email_sent": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "<PERSON>"}, "is_email_replied": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Is Email Replied"}, "is_sms_sent": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Is Sms Sent"}, "is_sms_replied": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Is Sms Replied"}}, "required": ["user_id"], "title": "fetch_customer_emailsArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Finding Total number of propsects based on ICP", "description": "Use Apollo API to find total number of prospects matching given ICP filters", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "conversation_id": {"title": "Conversation Id", "type": "string"}}, "required": ["user_id", "conversation_id"], "title": "count_icp_prospectsArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Fetching User Settings", "description": "Fetch settings for a user; if missing, create defaults and return", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}}, "required": ["user_id"], "title": "fetch_user_settingsArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Creating User Settings", "description": "Create settings for a user (idempotent; returns existing if present)", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "name": {"default": null, "title": "Name", "type": "string"}, "phone_number": {"default": null, "title": "Phone Number", "type": "string"}, "daily_email_limit": {"default": null, "title": "Daily Email Limit", "type": "integer"}, "increment_rate": {"default": null, "title": "Increment Rate", "type": "number"}, "email": {"default": null, "title": "Email", "type": "string"}, "timezone": {"default": null, "title": "Timezone", "type": "string"}, "provider": {"additionalProperties": true, "default": null, "title": "Provider", "type": "object"}}, "required": ["user_id"], "title": "create_user_settingsArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Updating User Settings", "description": "Update settings for a user", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "name": {"default": null, "title": "Name", "type": "string"}, "phone_number": {"default": null, "title": "Phone Number", "type": "string"}, "daily_email_limit": {"default": null, "title": "Daily Email Limit", "type": "integer"}, "increment_rate": {"default": null, "title": "Increment Rate", "type": "number"}, "email": {"default": null, "title": "Email", "type": "string"}, "timezone": {"default": null, "title": "Timezone", "type": "string"}, "provider": {"additionalProperties": true, "default": null, "title": "Provider", "type": "object"}}, "required": ["user_id"], "title": "update_user_settingsArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Fetching Available Workflow Types", "description": "Get list of available workflow type identifiers from admin settings", "input_schema": {"properties": {}, "title": "get_available_workflow_typesArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Configuring User Workflows", "description": "Configure user workflows by cloning all admin workflows for the user", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "name": {"title": "Name", "type": "string"}, "email": {"title": "Email", "type": "string"}, "timezone": {"title": "Timezone", "type": "string"}, "phone_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Phone Number"}, "provider": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "Provider"}, "daily_email_limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 50, "title": "Daily Email Limit"}, "increment_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": 0.2, "title": "Increment Rate"}}, "required": ["user_id", "name", "email", "timezone"], "title": "configure_user_workflowsArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Creating a New Email Conversation", "description": "Create a new email conversation between customer and user", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "campaign_id": {"title": "Campaign Id", "type": "string"}, "customer_id": {"title": "Customer Id", "type": "string"}, "subject": {"title": "Subject", "type": "string"}, "body": {"title": "Body", "type": "string"}, "sender_name": {"title": "Sender Name", "type": "string"}}, "required": ["user_id", "campaign_id", "customer_id", "subject", "body", "sender_name"], "title": "create_email_conversationArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Replying an Email Conversation from a Customer", "description": "Create an email conversation record from a customer's reply using from/to emails (resolves user_id, customer_id, latest campaign)", "input_schema": {"properties": {"from_email_address": {"title": "From Email Address", "type": "string"}, "to_email_address": {"title": "To Email Address", "type": "string"}, "subject": {"title": "Subject", "type": "string"}, "body": {"title": "Body", "type": "string"}}, "required": ["from_email_address", "to_email_address", "subject", "body"], "title": "reply_email_from_customerArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Fetching an Email Conversation", "description": "Get email conversation by ID", "input_schema": {"properties": {"conversation_id": {"title": "Conversation Id", "type": "string"}, "user_id": {"title": "User Id", "type": "string"}}, "required": ["conversation_id", "user_id"], "title": "get_email_conversationArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Fetching Email Conversations", "description": "Get email conversations with optional filtering", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "campaign_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Campaign Id"}, "customer_email": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Customer <PERSON><PERSON>"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Is Active"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Limit"}, "offset": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Offset"}}, "required": ["user_id"], "title": "get_email_conversationsArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Marking Email Conversations as Read", "description": "Mark all unread email conversations between a user and customer as read", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "customer_id": {"title": "Customer Id", "type": "string"}}, "required": ["user_id", "customer_id"], "title": "mark_email_conversations_as_readArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Fetching Email Conversations with Pa<PERSON>ation", "description": "Fetch email conversations with enhanced pagination, sorting, and is_more key", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "campaign_id": {"title": "Campaign Id", "type": "string"}, "customer_id": {"title": "Customer Id", "type": "string"}, "is_read": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "<PERSON>"}, "limit": {"default": 10, "title": "Limit", "type": "integer"}, "offset": {"default": 0, "title": "Offset", "type": "integer"}, "sort_by": {"default": "created_at", "title": "Sort By", "type": "string"}, "sort_order": {"default": "desc", "title": "Sort Order", "type": "string"}}, "required": ["user_id", "campaign_id", "customer_id"], "title": "fetch_email_conversations_with_paginationArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Creating a New SMS Conversation", "description": "Create a new SMS conversation between customer and user", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "campaign_id": {"title": "Campaign Id", "type": "string"}, "customer_id": {"title": "Customer Id", "type": "string"}, "customer_phone_number": {"title": "Customer Phone Number", "type": "string"}, "sender_name": {"title": "Sender Name", "type": "string"}, "message": {"title": "Message", "type": "string"}}, "required": ["user_id", "campaign_id", "customer_id", "customer_phone_number", "sender_name", "message"], "title": "create_sms_conversationArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Replying an SMS Conversation from a Customer", "description": "Create an SMS conversation record from a customer's reply using phone numbers (resolves user_id, customer_id, latest campaign)", "input_schema": {"properties": {"from_phone_number": {"title": "From Phone Number", "type": "string"}, "to_phone_number": {"title": "To Phone Number", "type": "string"}, "message": {"title": "Message", "type": "string"}}, "required": ["from_phone_number", "to_phone_number", "message"], "title": "reply_sms_from_customerArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Fetching an SMS Conversation", "description": "Get SMS conversation by ID", "input_schema": {"properties": {"conversation_id": {"title": "Conversation Id", "type": "string"}, "user_id": {"default": null, "title": "User Id", "type": "string"}}, "required": ["conversation_id"], "title": "get_sms_conversationArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Fetching SMS Conversations", "description": "Get SMS conversations with optional filtering", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "campaign_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Campaign Id"}, "customer_phone_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Customer Phone Number"}, "customer_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Customer Id"}, "sender_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Sender Name"}, "is_read": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "<PERSON>"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Limit"}, "offset": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Offset"}}, "required": ["user_id"], "title": "get_sms_conversationsArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Marking SMS Conversations as Read", "description": "Mark all unread SMS conversations between a user and customer as read", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "customer_id": {"title": "Customer Id", "type": "string"}}, "required": ["user_id", "customer_id"], "title": "mark_sms_conversations_as_readArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Fetching SMS Conversations with Pagination", "description": "Fetch SMS conversations with enhanced pagination, sorting, and is_more key", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "campaign_id": {"title": "Campaign Id", "type": "string"}, "customer_id": {"title": "Customer Id", "type": "string"}, "sender_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Sender Name"}, "is_read": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "<PERSON>"}, "limit": {"default": 10, "title": "Limit", "type": "integer"}, "offset": {"default": 0, "title": "Offset", "type": "integer"}, "sort_by": {"default": "created_at", "title": "Sort By", "type": "string"}, "sort_order": {"default": "desc", "title": "Sort Order", "type": "string"}}, "required": ["user_id", "campaign_id", "customer_id"], "title": "fetch_sms_conversations_with_paginationArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Fetching Campaign Aggregated Conversations", "description": "Aggregate email and SMS conversations by customer for a campaign, returning most recent conversation per customer with pagination", "input_schema": {"properties": {"campaign_id": {"title": "Campaign Id", "type": "string"}, "user_id": {"title": "User Id", "type": "string"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 10, "title": "Limit"}, "offset": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 0, "title": "Offset"}}, "required": ["campaign_id", "user_id"], "title": "get_campaign_aggregated_conversationsArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Fetching Customer Conversation Timeline", "description": "Aggregate email and SMS conversations for a customer into a unified timeline", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "customer_id": {"title": "Customer Id", "type": "string"}, "campaign_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Campaign Id"}, "limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Limit"}, "offset": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Offset"}}, "required": ["user_id", "customer_id"], "title": "get_customer_conversationArguments", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "Fetching Metrics", "description": "Retrieve metrics from the 'metrics' collection filtered by user, campaign, date range, and grouped by a period (day, week, month).", "input_schema": {"properties": {"user_id": {"title": "User Id", "type": "string"}, "campaign_id": {"title": "Campaign Id", "type": "string"}, "date_from": {"title": "Date From", "type": "string"}, "date_to": {"title": "Date To", "type": "string"}, "group_by": {"default": "day", "title": "Group By", "type": "string"}}, "required": ["user_id", "campaign_id", "date_from", "date_to"], "title": "get_metricsArguments", "type": "object"}, "output_schema": null, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}