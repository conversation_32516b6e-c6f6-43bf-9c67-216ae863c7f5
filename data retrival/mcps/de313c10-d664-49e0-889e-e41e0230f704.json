{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "de313c10-d664-49e0-889e-e41e0230f704", "name": "stock-video-generation-mcp", "logo": null, "description": "stock video generation ", "category": "marketing", "tags": ["stockvideo"], "created_at": "2025-06-15T13:34:20.621682", "updated_at": "2025-08-19T13:34:36.669597", "owner_id": "fce79072-a235-4127-ac5b-b5b1709a8077", "hosted_url": "https://stock-video-generation-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["generate_stock_video", "generate_ai_stock_video", "fetch_stock_videos"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "generate_stock_video", "description": "generate and find the stock video for the video", "input_schema": {"properties": {"script": {"description": "Script is required", "maxLength": 1000, "minLength": 1, "title": "<PERSON><PERSON><PERSON>", "type": "string"}}, "required": ["script"], "title": "GenerateStockVideo", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "generate_ai_stock_video", "description": "generate the ai stock video using the script", "input_schema": {"properties": {"script": {"description": "Script is required", "maxLength": 1000, "minLength": 1, "title": "<PERSON><PERSON><PERSON>", "type": "string"}}, "required": ["script"], "title": "GenerateAIStockVideo", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "fetch_stock_videos", "description": "fetch the stock videos from search terms", "input_schema": {"properties": {"search_terms": {"description": "Search terms are required", "items": {"maxLength": 100, "minLength": 1, "type": "string"}, "title": "Search Terms", "type": "array"}, "page": {"default": 1, "description": "Page number for pagination, default is 1", "minimum": 1, "title": "Page", "type": "integer"}, "page_size": {"default": 10, "description": "Number of results per page, default is 10", "minimum": 1, "title": "<PERSON>", "type": "integer"}}, "required": ["search_terms"], "title": "FetchStockVideos", "type": "object"}, "output_schema": null, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}