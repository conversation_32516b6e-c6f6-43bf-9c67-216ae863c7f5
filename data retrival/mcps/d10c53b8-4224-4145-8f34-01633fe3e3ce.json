{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "d10c53b8-4224-4145-8f34-01633fe3e3ce", "name": "Google Forms", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/Google_Forms_Logo-removebg-preview%20%281%29.png/1752823679-Google_Forms_Logo-removebg-preview1.png", "description": "Google Forms MCP ", "category": "general", "tags": ["forms", "google", "google forms"], "created_at": "2025-07-18T07:28:29.603297", "updated_at": "2025-08-05T10:38:03.586828", "owner_id": "598f7395-c39a-4bfa-9996-0e3bff89047c", "hosted_url": "https://google-form-mcp-server-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["get_google_form", "create_google_form", "validate_google_credentials", "get_google_form_responses", "update_google_form"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "get_google_form", "description": "Get a Google Form by ID", "input_schema": {"properties": {"form_id": {"title": "Form Id", "type": "string"}}, "required": ["form_id"], "title": "GetGoogleForm", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "create_google_form", "description": "Create a new Google Form", "input_schema": {"properties": {"title": {"title": "Title", "type": "string"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Description"}}, "required": ["title"], "title": "CreateGoogleForm", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "validate_google_credentials", "description": "Validate Google OAuth credentials and check API access", "input_schema": {"properties": {}, "title": "ValidateGoogleCredentials", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "get_google_form_responses", "description": "Get responses for a Google Form", "input_schema": {"properties": {"form_id": {"title": "Form Id", "type": "string"}}, "required": ["form_id"], "title": "GetGoogleFormResponses", "type": "object"}, "output_schema": {"properties": {"Responses": {"type": "array", "description": "Responses recieved for that form", "title": "Responses"}}}, "annotations": null}, {"name": "update_google_form", "description": "Update a Google Form with new questions", "input_schema": {"$defs": {"ChoiceQuestion": {"properties": {"type": {"enum": ["RADIO", "CHECKBOX", "DROP_DOWN"], "title": "Type", "type": "string"}, "options": {"items": {"$ref": "#/$defs/Option"}, "title": "Options", "type": "array"}, "shuffle": {"default": false, "title": "Shuffle", "type": "boolean"}}, "required": ["type", "options"], "title": "ChoiceQuestion", "type": "object"}, "CreateItemRequest": {"properties": {"item": {"$ref": "#/$defs/Item"}, "location": {"$ref": "#/$defs/Location"}}, "required": ["item", "location"], "title": "CreateItemRequest", "type": "object"}, "DateQuestion": {"properties": {"includeTime": {"default": false, "title": "Includetime", "type": "boolean"}, "includeYear": {"default": false, "title": "Includeyear", "type": "boolean"}}, "title": "DateQuestion", "type": "object"}, "DeleteItemRequest": {"properties": {"location": {"$ref": "#/$defs/Location"}}, "required": ["location"], "title": "DeleteItemRequest", "type": "object"}, "FileUploadQuestion": {"properties": {"folderId": {"title": "Folderid", "type": "string"}, "types": {"items": {"type": "string"}, "title": "Types", "type": "array"}, "maxFiles": {"title": "Max<PERSON>les", "type": "integer"}, "maxFileSize": {"title": "Maxfilesize", "type": "string"}}, "required": ["folderId", "types", "maxFiles", "maxFileSize"], "title": "FileUploadQuestion", "type": "object"}, "Item": {"properties": {"title": {"title": "Title", "type": "string"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Description"}, "questionItem": {"anyOf": [{"$ref": "#/$defs/QuestionItem"}, {"type": "null"}], "default": null}}, "required": ["title"], "title": "<PERSON><PERSON>", "type": "object"}, "Location": {"properties": {"index": {"title": "Index", "type": "integer"}}, "required": ["index"], "title": "Location", "type": "object"}, "MoveItemRequest": {"properties": {"original_location": {"$ref": "#/$defs/Location"}, "new_location": {"$ref": "#/$defs/Location"}}, "required": ["original_location", "new_location"], "title": "MoveItemRequest", "type": "object"}, "Option": {"properties": {"value": {"title": "Value", "type": "string"}}, "required": ["value"], "title": "Option", "type": "object"}, "Question": {"properties": {"required": {"default": false, "title": "Required", "type": "boolean"}, "textQuestion": {"anyOf": [{"$ref": "#/$defs/TextQuestion"}, {"type": "null"}], "default": null}, "choiceQuestion": {"anyOf": [{"$ref": "#/$defs/ChoiceQuestion"}, {"type": "null"}], "default": null}, "scaleQuestion": {"anyOf": [{"$ref": "#/$defs/ScaleQuestion"}, {"type": "null"}], "default": null}, "dateQuestion": {"anyOf": [{"$ref": "#/$defs/DateQuestion"}, {"type": "null"}], "default": null}, "timeQuestion": {"anyOf": [{"$ref": "#/$defs/TimeQuestion"}, {"type": "null"}], "default": null}, "fileUploadQuestion": {"anyOf": [{"$ref": "#/$defs/FileUploadQuestion"}, {"type": "null"}], "default": null}}, "title": "Question", "type": "object"}, "QuestionItem": {"properties": {"question": {"$ref": "#/$defs/Question"}}, "required": ["question"], "title": "QuestionItem", "type": "object"}, "Request": {"properties": {"createItem": {"anyOf": [{"$ref": "#/$defs/CreateItemRequest"}, {"type": "null"}], "default": null}, "updateFormInfo": {"anyOf": [{"$ref": "#/$defs/UpdateFormInfoRequest"}, {"type": "null"}], "default": null}, "updateItem": {"anyOf": [{"$ref": "#/$defs/UpdateItemRequest"}, {"type": "null"}], "default": null}, "deleteItem": {"anyOf": [{"$ref": "#/$defs/DeleteItemRequest"}, {"type": "null"}], "default": null}, "moveItem": {"anyOf": [{"$ref": "#/$defs/MoveItemRequest"}, {"type": "null"}], "default": null}}, "title": "Request", "type": "object"}, "ScaleQuestion": {"properties": {"low": {"title": "Low", "type": "integer"}, "high": {"title": "High", "type": "integer"}, "lowLabel": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Lowlabel"}, "highLabel": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Highlabel"}}, "required": ["low", "high"], "title": "ScaleQuestion", "type": "object"}, "TextQuestion": {"properties": {"paragraph": {"default": false, "title": "Paragraph", "type": "boolean"}}, "title": "TextQuestion", "type": "object"}, "TimeQuestion": {"properties": {"duration": {"default": false, "title": "Duration", "type": "boolean"}}, "title": "TimeQuestion", "type": "object"}, "UpdateFormInfoRequest": {"properties": {"info": {"additionalProperties": true, "title": "Info", "type": "object"}, "updateMask": {"title": "Updatemask", "type": "string"}}, "required": ["info", "updateMask"], "title": "UpdateFormInfoRequest", "type": "object"}, "UpdateItemRequest": {"properties": {"item": {"$ref": "#/$defs/Item"}, "location": {"$ref": "#/$defs/Location"}, "updateMask": {"title": "Updatemask", "type": "string"}}, "required": ["item", "location", "updateMask"], "title": "UpdateItemRequest", "type": "object"}}, "properties": {"form_id": {"title": "Form Id", "type": "string"}, "requests": {"description": "A list of requests to update the form. See the Google Forms API documentation for more details.", "items": {"$ref": "#/$defs/Request"}, "title": "Requests", "type": "array"}}, "required": ["form_id", "requests"], "title": "UpdateGoogleForm", "type": "object"}, "output_schema": {"type": "object", "properties": {"result": {"type": "string", "description": "result", "title": "result"}}, "required": ["result"]}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": ["bff105a7-1d93-422d-9980-ecb3aea1e3d2"], "url": null}}