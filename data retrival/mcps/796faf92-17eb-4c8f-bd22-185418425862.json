{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "796faf92-17eb-4c8f-bd22-185418425862", "name": "Website Generator", "logo": null, "description": "Website Generator MCP Server.", "category": "general", "tags": null, "created_at": "2025-07-03T11:41:52.575486", "updated_at": "2025-07-24T16:59:29.371058", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "hosted_url": null, "mcp_type": "stdio", "owner_name": "<PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": "https://github.com/<PERSON><PERSON>-Patil-RI/website-generator", "api_documentation": null, "capabilities": ["repo_setup", "create_file", "push_changes", "read_file", "list_files", "update_file"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "repo_setup", "description": "\n    Setup a new repository for a website project by cloning a template, creating a GitHub repo, and pushing the code.\n\n    This tool performs the following steps:\n    1. <PERSON>lone the React Vite template repository directly to base directory\n    2. Remove the .git folder from the cloned repository\n    3. Create a new repository on GitHub with the given project name\n    4. Push the code to the new GitHub repository\n    5. Optionally deploy to AWS Amplify (if requested)\n\n    Args:\n        project_name: Name of the project and GitHub repository\n        description: Optional description for the GitHub repository\n        deploy_to_amplify: Whether to deploy to AWS Amplify (optional, not implemented yet)\n\n    Returns:\n        Status message with repository URL and deployment information\n    ", "input_schema": {"properties": {"project_name": {"title": "Project Name", "type": "string"}, "description": {"default": "", "title": "Description", "type": "string"}, "deploy_to_amplify": {"default": false, "title": "Deploy To Amplify", "type": "boolean"}}, "required": ["project_name"], "title": "repo_setupArguments", "type": "object"}, "output_schema": {"properties": {"result": {"title": "Result", "type": "string"}}, "required": ["result"], "title": "repo_setupOutput", "type": "object"}, "annotations": null}, {"name": "create_file", "description": "\n    Create a new file in the project with content generated by the AI Agent.\n\n    This tool performs the following steps:\n    1. Get the file_name, file_path and content from the AI Agent\n    2. Create a new file with the given file_name at the file_path\n    3. Write the content in the file\n\n    Args:\n        file_name: Name of the file to create (e.g., \"index.html\", \"app.js\")\n        file_path: Directory path where the file should be created (e.g., \"./src\", \"./public\")\n        content: Content to write to the file\n\n    Returns:\n        Status message indicating success or failure\n    ", "input_schema": {"properties": {"file_name": {"title": "File Name", "type": "string"}, "file_path": {"title": "File Path", "type": "string"}, "content": {"title": "Content", "type": "string"}}, "required": ["file_name", "file_path", "content"], "title": "create_fileArguments", "type": "object"}, "output_schema": {"properties": {"result": {"title": "Result", "type": "string"}}, "required": ["result"], "title": "create_fileOutput", "type": "object"}, "annotations": null}, {"name": "push_changes", "description": "\n    Commit the changes made to the project and push them to the remote repository.\n\n    This tool performs the following steps:\n    1. Get the project name from the AI Agent\n    2. Locate the project directory in the base directory\n    3. Check if there are any changes made to the project\n    4. If there are no changes, it will return\n    5. If there are changes, it will stage the changes\n    6. Commit the changes with an auto-generated commit message\n    7. Push the changes to the remote repository\n\n    Args:\n        project_name: Name of the project (same as used in repo_setup)\n\n    Returns:\n        Status message indicating success or failure\n    ", "input_schema": {"properties": {"project_name": {"title": "Project Name", "type": "string"}}, "required": ["project_name"], "title": "push_changesArguments", "type": "object"}, "output_schema": {"properties": {"result": {"title": "Result", "type": "string"}}, "required": ["result"], "title": "push_changesOutput", "type": "object"}, "annotations": null}, {"name": "read_file", "description": "\n    Read the contents of a file.\n\n    This tool allows reading the contents of any file in the project or system.\n    Useful for examining configuration files, source code, or any text-based files.\n\n    Args:\n        file_path: Path to the file to read (can be relative or absolute)\n\n    Returns:\n        File contents or error message\n    ", "input_schema": {"properties": {"file_path": {"title": "File Path", "type": "string"}}, "required": ["file_path"], "title": "read_fileArguments", "type": "object"}, "output_schema": {"properties": {"result": {"title": "Result", "type": "string"}}, "required": ["result"], "title": "read_fileOutput", "type": "object"}, "annotations": null}, {"name": "list_files", "description": "\n    List all files and directories in the specified directory.\n\n    This tool provides a directory listing showing files and subdirectories.\n    Files are marked with [FILE] and directories with [DIR] prefixes.\n\n    Args:\n        directory_path: Path to the directory to list (can be relative or absolute)\n\n    Returns:\n        Directory listing or error message\n    ", "input_schema": {"properties": {"directory_path": {"title": "Directory Path", "type": "string"}}, "required": ["directory_path"], "title": "list_filesArguments", "type": "object"}, "output_schema": {"properties": {"result": {"title": "Result", "type": "string"}}, "required": ["result"], "title": "list_filesOutput", "type": "object"}, "annotations": null}, {"name": "update_file", "description": "\n    Update the contents of an existing file.\n\n    This tool overwrites the entire content of an existing file with new content.\n    The file must already exist - use create_file to create new files.\n\n    Args:\n        file_path: Path to the file to update (can be relative or absolute)\n        new_content: New content to write to the file\n\n    Returns:\n        Success message or error message\n    ", "input_schema": {"properties": {"file_path": {"title": "File Path", "type": "string"}, "new_content": {"title": "New Content", "type": "string"}}, "required": ["file_path", "new_content"], "title": "update_fileArguments", "type": "object"}, "output_schema": {"properties": {"result": {"title": "Result", "type": "string"}}, "required": ["result"], "title": "update_fileOutput", "type": "object"}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": "website-generator", "git_user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>-RI", "integrations": null, "url": null}}