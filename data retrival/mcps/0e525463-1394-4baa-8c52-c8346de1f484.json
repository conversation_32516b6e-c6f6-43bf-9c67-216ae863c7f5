{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "0e525463-1394-4baa-8c52-c8346de1f484", "name": "cal.com", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/logo.png/1751449779-logo.png", "description": "cal.con mcp server", "category": "general", "tags": null, "created_at": "2025-07-02T09:49:55.200912", "updated_at": "2025-08-18T16:31:17.766620", "owner_id": "6e75537b-e7dd-4f2e-9455-2b9acce6f351", "hosted_url": "https://cal-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["list_bookings", "create_booking", "list_event_types", "check_availability"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "list_bookings", "description": "Get all Cal.com bookings for a specified time range. Returns a concise list with essential booking information.", "input_schema": {"description": "Schema for listing Cal.com bookings", "properties": {"limit": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 100, "description": "Maximum number of bookings to return", "title": "Limit"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Filter by booking status (confirmed, cancelled, etc.)", "title": "Status"}, "days_back": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 30, "description": "Number of days back to search", "title": "Days Back"}, "days_forward": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 30, "description": "Number of days forward to search", "title": "Days Forward"}}, "title": "ListBookings", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_booking", "description": "Create a new booking in Cal.com. Requires event type ID, start time, attendee details, and timezone.", "input_schema": {"description": "Schema for creating a new Cal.com booking", "properties": {"event_type_id": {"description": "Cal.com Event Type ID (get this from list_event_types)", "title": "Event Type Id", "type": "integer"}, "start_time": {"description": "Start time in ISO format (e.g., '2024-01-15T14:00:00')", "title": "Start Time", "type": "string"}, "attendee_name": {"description": "Full name of the attendee", "title": "Attendee Name", "type": "string"}, "attendee_email": {"description": "Email address of the attendee", "title": "<PERSON><PERSON><PERSON>", "type": "string"}, "timezone": {"description": "Timezone (e.g., 'America/New_York', 'UTC')", "title": "Timezone", "type": "string"}}, "required": ["event_type_id", "start_time", "attendee_name", "attendee_email", "timezone"], "title": "CreateBooking", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "list_event_types", "description": "Get all available Cal.com event types with their IDs, names, and durations", "input_schema": {"description": "Schema for listing Cal.com event types - no parameters required", "properties": {}, "title": "ListEventTypes", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "check_availability", "description": "Check available time slots for a Cal.com event type on a specific date", "input_schema": {"description": "Schema for checking availability for a Cal.com event type", "properties": {"event_type_id": {"description": "Cal.com Event Type ID (get this from list_event_types)", "title": "Event Type Id", "type": "integer"}, "date": {"description": "Date to check in YYYY-MM-DD format (e.g., '2024-01-15')", "title": "Date", "type": "string"}, "timezone": {"description": "Timezone (e.g., 'America/New_York', 'Asia/Calcutta')", "title": "Timezone", "type": "string"}}, "required": ["event_type_id", "date", "timezone"], "title": "CheckAvailability", "type": "object"}, "output_schema": null, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}