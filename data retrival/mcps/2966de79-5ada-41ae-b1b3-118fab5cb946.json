{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "2966de79-5ada-41ae-b1b3-118fab5cb946", "name": "video-generation", "logo": null, "description": "Video generation mcp server", "category": "general", "tags": ["AI", "Automation"], "created_at": "2025-06-26T08:14:35.895911", "updated_at": "2025-07-11T11:03:42.082100", "owner_id": "180c7469-1db0-4707-bc17-eeaa5e7ff64d", "hosted_url": "https://video-generation-mcp-server-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["generate_video"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "generate_video", "description": "generate and process the video", "input_schema": {"$defs": {"Scene": {"properties": {"start_image": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Start Image"}, "video": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Video"}, "end_image": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "End Image"}}, "title": "Scene", "type": "object"}, "VideoViewType": {"enum": ["LANDSCAPE", "PORTRAIT", "SQUARE"], "title": "VideoViewType", "type": "string"}}, "properties": {"view_type": {"$ref": "#/$defs/VideoViewType"}, "scenes": {"default": [], "items": {"$ref": "#/$defs/Scene"}, "title": "Scenes", "type": "array"}, "audio_urls": {"items": {"format": "uri", "maxLength": 2083, "minLength": 1, "type": "string"}, "title": "Audio Urls", "type": "array"}, "subtitles": {"minLength": 1, "title": "Subtitles", "type": "string"}}, "required": ["view_type", "audio_urls", "subtitles"], "title": "GenerateVideoObject", "type": "object"}, "output_schema": {"type": "object", "properties": {"thumbnail": {"type": "object", "properties": {"url": {"type": "string", "format": "uri"}, "mimetype": {"type": "string"}}}, "video_link": {"type": "object", "properties": {"url": {"type": "string", "format": "uri"}, "mimetype": {"type": "string"}}}, "duration": {"type": "number"}}}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}