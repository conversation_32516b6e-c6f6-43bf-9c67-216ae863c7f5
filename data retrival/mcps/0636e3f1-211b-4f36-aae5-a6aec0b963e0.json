{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "0636e3f1-211b-4f36-aae5-a6aec0b963e0", "name": "<PERSON><PERSON>", "logo": null, "description": "server to initiate calls.", "category": "sales", "tags": null, "created_at": "2025-08-21T10:50:33.952678", "updated_at": "2025-08-21T12:23:20.262172", "owner_id": "6e75537b-e7dd-4f2e-9455-2b9acce6f351", "hosted_url": "https://ruh-voice-mcp-dev-624209391722.us-central1.run.app/mcp", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["single_phone_call", "phone_call_with_system_message"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "single_phone_call", "description": "Initiate a phone outbound call using phone-based agent lookup.\n\nThis endpoint allows making outbound calls without demo call restrictions.\nRequires valid phone numbers and proper user authentication via public key.\n\nArgs:\n    from_phone: Originating phone number (used to lookup agent). Must be a valid phone number format.\n    to_phone: Destination phone number. Must be a valid phone number format.\n    public_key: Public key for user authentication (required). Must be a valid public key.\n    metadata: Optional metadata for the call (JSON string). Can contain additional call context or parameters.\n\nReturns:\n    JSON string containing the call result with success status, message, and call_id if successful.", "input_schema": {"properties": {"from_phone": {"title": "From Phone", "type": "string"}, "to_phone": {"title": "To Phone", "type": "string"}, "public_key": {"title": "Public Key", "type": "string"}, "metadata": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON><PERSON>"}}, "required": ["from_phone", "to_phone", "public_key"], "type": "object"}, "output_schema": {"properties": {"e_ctc": {"type": "number", "description": "Expected ctc", "title": "e_ctc"}, "notice_period": {"type": "number", "description": "Notice period in days", "title": "notice_period"}, "interview_time": {"type": "string", "description": "The required interview time", "title": "interview_time"}}}, "annotations": null}, {"name": "phone_call_with_system_message", "description": "Initiate a node outbound call with system message template processing.\n\nProcesses system_message template with metadata placeholders and includes it in room metadata.\nThe server-side API handles template processing (replacing placeholders like {name}, {phoneNumber}\nwith values from metadata dictionary).\n\nArgs:\n    from_phone: Originating phone number (used to lookup agent). Must be a valid phone number format.\n    to_phone: Destination phone number. Must be a valid phone number format.\n    system_message: System message template with placeholders (e.g., 'Hello {name}, your phone is {phoneNumber}').\n    metadata: Metadata dictionary for template placeholder replacement (e.g., {\"name\": \"<PERSON>\", \"phoneNumber\": \"************\"}).\n    public_key: Public key for user authentication (required). Must be a valid public key.\n\nReturns:\n    JSON string containing the call result with success status, message, call_id, and processed_system_message if successful.", "input_schema": {"properties": {"from_phone": {"title": "From Phone", "type": "string"}, "to_phone": {"title": "To Phone", "type": "string"}, "system_message": {"title": "System Message", "type": "string"}, "metadata": {"additionalProperties": true, "title": "<PERSON><PERSON><PERSON>", "type": "object"}, "public_key": {"title": "Public Key", "type": "string"}}, "required": ["from_phone", "to_phone", "system_message", "metadata", "public_key"], "type": "object"}, "output_schema": {}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}