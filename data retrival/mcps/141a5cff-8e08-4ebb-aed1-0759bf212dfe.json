{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "name": "Rapid HRMS", "logo": null, "description": "HRMS provided by RapidInnovation Technologies", "category": "general", "tags": ["hrms"], "created_at": "2025-07-24T12:55:59.295585", "updated_at": "2025-07-29T06:49:42.072406", "owner_id": "5229e05b-aaea-4850-9972-7268559318cf", "hosted_url": "https://hrms-mcp.rapidinnovation.tech/mcp/", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["leave_get_employee_leave_balance", "leave_get_leave_types", "leave_search_employees", "leave_get_leave_requests", "leave_create_leave_request", "attendance_get_employee_attendance", "attendance_get_attendance_summary", "attendance_clock_in_employee", "attendance_clock_out_employee", "attendance_get_overtime_records", "attendance_get_late_early_records", "attendance_get_attendance_activities", "finance_get_exchange_rates", "finance_generate_monthly_report", "project_list_projects", "project_filter_projects_by_balance", "project_fetch_my_projects", "project_get_project_details", "project_assign_team_member", "project_get_project_team", "employee_fetch_employee_profile"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "leave_get_employee_leave_balance", "description": "Fetch leave balance for a specific employee. Optionally filter by leave type.", "input_schema": {"properties": {"include_carryforward": {"default": null, "title": "Include Carryforward", "type": "boolean"}, "leave_type_id": {"default": null, "title": "Leave Type Id", "type": "integer"}}, "type": "object"}, "output_schema": null, "annotations": null}, {"name": "leave_get_leave_types", "description": "Get all available leave types in the system", "input_schema": {"properties": {}, "type": "object"}, "output_schema": null, "annotations": null}, {"name": "leave_search_employees", "description": "Search for employees by name or badge ID", "input_schema": {"properties": {"query": {"title": "Query", "type": "string"}, "limit": {"default": null, "title": "Limit", "type": "integer"}}, "required": ["query"], "type": "object"}, "output_schema": null, "annotations": null}, {"name": "leave_get_leave_requests", "description": "Get leave requests with optional filtering", "input_schema": {"properties": {"status": {"default": null, "title": "Status", "type": "string"}, "start_date": {"default": null, "title": "Start Date", "type": "string"}, "end_date": {"default": null, "title": "End Date", "type": "string"}, "page": {"default": null, "title": "Page", "type": "integer"}, "page_size": {"default": null, "title": "<PERSON>", "type": "integer"}}, "type": "object"}, "output_schema": null, "annotations": null}, {"name": "leave_create_leave_request", "description": "Create a new leave request", "input_schema": {"properties": {"leave_type": {"title": "Leave Type", "type": "string"}, "start_date": {"title": "Start Date", "type": "string"}, "end_date": {"title": "End Date", "type": "string"}, "description": {"title": "Description", "type": "string"}}, "required": ["leave_type", "start_date", "end_date", "description"], "type": "object"}, "output_schema": null, "annotations": null}, {"name": "attendance_get_employee_attendance", "description": "Get attendance records for a specific employee", "input_schema": {"properties": {"auth_token": {"title": "<PERSON><PERSON>", "type": "string"}, "employee_id": {"title": "Employee Id", "type": "integer"}, "start_date": {"default": null, "title": "Start Date", "type": "string"}, "end_date": {"default": null, "title": "End Date", "type": "string"}, "page": {"default": null, "title": "Page", "type": "integer"}, "page_size": {"default": null, "title": "<PERSON>", "type": "integer"}}, "required": ["auth_token", "employee_id"], "type": "object"}, "output_schema": null, "annotations": null}, {"name": "attendance_get_attendance_summary", "description": "Get attendance summary for employees", "input_schema": {"properties": {"auth_token": {"title": "<PERSON><PERSON>", "type": "string"}, "date": {"title": "Date", "type": "string"}, "department_filter": {"default": null, "title": "Department Filter", "type": "string"}, "status_filter": {"default": null, "title": "Status Filter", "type": "string"}}, "required": ["auth_token", "date"], "type": "object"}, "output_schema": null, "annotations": null}, {"name": "attendance_clock_in_employee", "description": "Clock in an employee for attendance", "input_schema": {"properties": {"auth_token": {"title": "<PERSON><PERSON>", "type": "string"}, "employee_id": {"title": "Employee Id", "type": "integer"}, "clock_in_time": {"default": null, "title": "Clock In Time", "type": "string"}, "date": {"default": null, "title": "Date", "type": "string"}}, "required": ["auth_token", "employee_id"], "type": "object"}, "output_schema": null, "annotations": null}, {"name": "attendance_clock_out_employee", "description": "Clock out an employee for attendance", "input_schema": {"properties": {"auth_token": {"title": "<PERSON><PERSON>", "type": "string"}, "employee_id": {"title": "Employee Id", "type": "integer"}, "clock_out_time": {"default": null, "title": "Clock Out Time", "type": "string"}, "date": {"default": null, "title": "Date", "type": "string"}}, "required": ["auth_token", "employee_id"], "type": "object"}, "output_schema": null, "annotations": null}, {"name": "attendance_get_overtime_records", "description": "Get overtime records for employees", "input_schema": {"properties": {"auth_token": {"title": "<PERSON><PERSON>", "type": "string"}, "employee_id": {"default": null, "title": "Employee Id", "type": "integer"}, "month": {"default": null, "title": "Month", "type": "integer"}, "year": {"default": null, "title": "Year", "type": "integer"}, "page": {"default": null, "title": "Page", "type": "integer"}, "page_size": {"default": null, "title": "<PERSON>", "type": "integer"}}, "required": ["auth_token"], "type": "object"}, "output_schema": null, "annotations": null}, {"name": "attendance_get_late_early_records", "description": "Get late come and early out records", "input_schema": {"properties": {"auth_token": {"title": "<PERSON><PERSON>", "type": "string"}, "employee_id": {"default": null, "title": "Employee Id", "type": "integer"}, "start_date": {"default": null, "title": "Start Date", "type": "string"}, "end_date": {"default": null, "title": "End Date", "type": "string"}, "type": {"default": null, "title": "Type", "type": "string"}, "page": {"default": null, "title": "Page", "type": "integer"}, "page_size": {"default": null, "title": "<PERSON>", "type": "integer"}}, "required": ["auth_token"], "type": "object"}, "output_schema": null, "annotations": null}, {"name": "attendance_get_attendance_activities", "description": "Get attendance activities (clock in/out events)", "input_schema": {"properties": {"auth_token": {"title": "<PERSON><PERSON>", "type": "string"}, "employee_id": {"default": null, "title": "Employee Id", "type": "integer"}, "date": {"default": null, "title": "Date", "type": "string"}, "activity_type": {"default": null, "title": "Activity Type", "type": "string"}}, "required": ["auth_token"], "type": "object"}, "output_schema": null, "annotations": null}, {"name": "finance_get_exchange_rates", "description": "Fetch current currency exchange rates", "input_schema": {"properties": {}, "type": "object"}, "output_schema": null, "annotations": null}, {"name": "finance_generate_monthly_report", "description": "Generate finance monthly report", "input_schema": {"properties": {"month": {"title": "Month", "type": "integer"}, "year": {"title": "Year", "type": "integer"}}, "required": ["month", "year"], "type": "object"}, "output_schema": null, "annotations": null}, {"name": "project_list_projects", "description": "List all active projects of the organization", "input_schema": {"properties": {}, "type": "object"}, "output_schema": null, "annotations": null}, {"name": "project_filter_projects_by_balance", "description": "Filter projects by open balance amount with various comparison operators", "input_schema": {"properties": {"balance_amount": {"title": "Balance Amount"}, "operator": {"title": "Operator", "type": "string"}, "include_null": {"default": null, "title": "Include Null", "type": "boolean"}}, "required": ["balance_amount", "operator"], "type": "object"}, "output_schema": null, "annotations": null}, {"name": "project_fetch_my_projects", "description": "<PERSON><PERSON> projects where I am a team member.", "input_schema": {"properties": {}, "type": "object"}, "output_schema": null, "annotations": null}, {"name": "project_get_project_details", "description": "Fetch detailed information for a specific project by its name.", "input_schema": {"properties": {"project_name": {"title": "Project Name", "type": "string"}}, "required": ["project_name"], "type": "object"}, "output_schema": null, "annotations": null}, {"name": "project_assign_team_member", "description": "Assign a team member to a project by its name.", "input_schema": {"properties": {"project_name": {"title": "Project Name", "type": "string"}, "employee_id": {"title": "Employee Id", "type": "integer"}}, "required": ["project_name", "employee_id"], "type": "object"}, "output_schema": null, "annotations": null}, {"name": "project_get_project_team", "description": "Fetch the team members for a specific project by its name.", "input_schema": {"properties": {"project_name": {"title": "Project Name", "type": "string"}}, "required": ["project_name"], "type": "object"}, "output_schema": null, "annotations": null}, {"name": "employee_fetch_employee_profile", "description": "Fetch public profile information for an employee by name. Defaults to your own profile if no name is provided.", "input_schema": {"properties": {"employee_name": {"default": null, "title": "Employee Name", "type": "string"}}, "type": "object"}, "output_schema": null, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": ["e45063f5-09c4-40d1-8977-d2b16601a4d4"], "url": null}}