{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "f506eb7c-a648-4873-8055-e2273be08478", "name": "Code-Runner-Mcp", "logo": null, "description": "Used to execute the code and returns the output. Now only supports Javascript. \nhttps://smithery.ai/server/@dravidsajinraj-iex/code-runner-mcp ", "category": "engineering", "tags": ["code", "code executor", "code runner"], "created_at": "2025-07-02T10:35:41.180971", "updated_at": "2025-08-22T10:06:33.574903", "owner_id": "4f19b076-b555-4d69-ba27-59dc10ecbf58", "hosted_url": "https://server.smithery.ai/@dravid<PERSON>jinraj-iex/code-runner-mcp/mcp?api_key=2bf9a5dd-cc32-424f-a0c3-cdc9031c3799&profile=mechanical-snail-g1RifI", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["execute_code", "get_capabilities", "validate_code", "execute_code_with_variables"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "execute_code", "description": "Execute JavaScript or Python code securely with comprehensive error handling and security measures", "input_schema": {"type": "object", "properties": {"language": {"type": "string", "enum": ["javascript", "python"], "description": "Programming language to execute"}, "code": {"type": "string", "description": "Code to execute"}, "input": {"type": "string", "description": "Input data for the program (stdin)"}, "timeout": {"type": "number", "description": "Execution timeout in milliseconds (max 60000)"}, "memoryLimit": {"type": "number", "description": "Memory limit in MB (max 512)"}, "enableNetworking": {"type": "boolean", "description": "Enable network access for this execution"}}, "required": ["language", "code"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"success": {"type": "boolean"}, "output": {"type": "string"}, "errorOutput": {"type": "string"}, "returnValue": {"type": "string"}, "executionTime": {"type": "number"}, "memoryUsed": {"type": "number"}, "language": {"type": "string"}}, "required": ["success", "output", "errorOutput", "returnValue", "executionTime", "memoryUsed", "language"], "additionalProperties": false}, "annotations": null}, {"name": "get_capabilities", "description": "Get information about supported languages and execution capabilities", "input_schema": {"type": "object", "properties": {}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "validate_code", "description": "Validate code for security and syntax issues without executing it", "input_schema": {"type": "object", "properties": {"language": {"type": "string", "enum": ["javascript", "python"], "description": "Programming language"}, "code": {"type": "string", "description": "Code to validate"}}, "required": ["language", "code"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": null, "annotations": null}, {"name": "execute_code_with_variables", "description": "Execute JavaScript or Python code with dynamic input variables that can be defined and passed as key-value pairs", "input_schema": {"type": "object", "properties": {"language": {"type": "string", "enum": ["javascript", "python"], "description": "Programming language to execute"}, "code": {"type": "string", "description": "Code to execute"}, "variables": {"anyOf": [{"type": "object", "additionalProperties": {}}, {"type": "string"}], "description": "Dynamic input variables as key-value pairs. Can be a JSON object or a JSON string (e.g., {\"name\": \"<PERSON>\", \"age\": 25, \"items\": [1,2,3]} or \"{\\\"name\\\": \\\"John\\\", \\\"age\\\": 25}\")"}, "input": {"type": "string", "description": "Additional input data for the program (stdin)"}, "timeout": {"type": "number", "description": "Execution timeout in milliseconds (max 60000)"}, "memoryLimit": {"type": "number", "description": "Memory limit in MB (max 512)"}, "enableNetworking": {"type": "boolean", "description": "Enable network access for this execution"}}, "required": ["language", "code"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"properties": {"hello": {"type": "string", "description": "hello", "title": "hello"}}}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}