{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "d129be85-8c05-4f45-b742-f33101082ee9", "name": "Strapi MCP", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/9c68ae10-0a8a-4e3f-9084-3625b19df9cb.png/1756663117-9c68ae10-0a8a-4e3f-9084-3625b19df9cb.png", "description": "Manage Strapi content and media from one place. Browse content types and components, run REST operations, and upload assets. ", "category": "general", "tags": null, "created_at": "2025-08-31T17:58:44.042765", "updated_at": "2025-09-01T06:51:00.765397", "owner_id": "2051be58-0123-407f-892f-cbe74966f0ab", "hosted_url": "https://server.smithery.ai/@AdityaRapid/strapi_mcps/mcp?api_key=ccf813d1-f1f1-49d1-923f-265d462eb4ac&profile=genuine-worm-wAjvKW", "mcp_type": "streamable-http", "owner_name": "<PERSON><PERSON><PERSON> <PERSON>h", "average_rating": null, "visibility": "public", "git_url": null, "api_documentation": null, "capabilities": ["strapi_get_components", "strapi_list_servers", "strapi_upload_media", "strapi_rest", "strapi_get_content_types"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "strapi_get_components", "description": "Get component schemas from Strapi", "input_schema": {"type": "object", "properties": {"server": {"type": "string", "description": "Server name"}}, "required": ["server"]}, "output_schema": {"type": "object", "properties": {"components": {"type": "array", "description": "List of Strapi components", "items": {"type": "object", "properties": {"uid": {"type": "string", "description": "Component unique identifier (e.g., shared.media)"}, "category": {"type": "string", "description": "Component category (e.g., shared, layout)"}, "displayName": {"type": "string", "description": "Human-readable component name"}, "icon": {"type": "string", "description": "Component icon identifier"}, "attributes": {"type": "object", "description": "Component field definitions", "additionalProperties": {"type": "object", "properties": {"type": {"type": "string", "description": "Field type"}, "required": {"type": "boolean", "description": "Whether field is required"}, "repeatable": {"type": "boolean", "description": "Whether component is repeatable"}}}}}, "required": ["uid", "category", "displayName", "attributes"]}}, "categories": {"type": "array", "description": "Available component categories", "items": {"type": "string"}}, "totalCount": {"type": "integer", "description": "Total number of components"}, "serverName": {"type": "string", "description": "Name of the Strapi server"}}, "required": ["components", "categories", "totalCount"]}, "annotations": null}, {"name": "strapi_list_servers", "description": "List all configured Strapi servers", "input_schema": {"type": "object", "properties": {}}, "output_schema": {"type": "object", "properties": {"servers": {"type": "array", "description": "List of configured Strapi servers", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Server name/identifier"}, "url": {"type": "string", "description": "Strapi server URL"}, "apiPrefix": {"type": "string", "description": "API prefix path (e.g., /api/v1)"}, "status": {"type": "string", "enum": ["connected", "configured", "error"], "description": "Connection status"}}, "required": ["name", "url", "status"]}}, "totalCount": {"type": "integer", "description": "Total number of configured servers"}, "message": {"type": "string", "description": "Status message"}}, "required": ["servers", "totalCount"]}, "annotations": null}, {"name": "strapi_upload_media", "description": "Upload media files to Strapi", "input_schema": {"type": "object", "properties": {"server": {"type": "string", "description": "Server name"}, "file_data": {"type": "string", "description": "Base64 encoded file data"}, "filename": {"type": "string", "description": "File name"}}, "required": ["server", "file_data", "filename"]}, "output_schema": {"type": "object", "properties": {"uploadedFiles": {"type": "array", "description": "List of successfully uploaded media files", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "File ID in Strapi"}, "documentId": {"type": "string", "description": "Document ID (Strapi v5+)"}, "name": {"type": "string", "description": "Original filename"}, "alternativeText": {"type": ["string", "null"], "description": "Alt text for accessibility"}, "caption": {"type": ["string", "null"], "description": "File caption"}, "width": {"type": ["integer", "null"], "description": "Image width in pixels"}, "height": {"type": ["integer", "null"], "description": "Image height in pixels"}, "formats": {"type": ["object", "null"], "description": "Available image formats and thumbnails", "additionalProperties": {"type": "object", "properties": {"url": {"type": "string", "description": "Format URL"}, "width": {"type": "integer", "description": "Format width"}, "height": {"type": "integer", "description": "Format height"}, "size": {"type": "number", "description": "Format file size in KB"}}}}, "hash": {"type": "string", "description": "Unique file hash"}, "ext": {"type": "string", "description": "File extension (e.g., '.png', '.jpg')"}, "mime": {"type": "string", "description": "MIME type (e.g., 'image/png', 'application/pdf')"}, "size": {"type": "number", "description": "File size in KB"}, "url": {"type": "string", "description": "Public URL path of the uploaded file"}, "previewUrl": {"type": ["string", "null"], "description": "Preview URL for the file"}, "provider": {"type": "string", "description": "Upload provider (e.g., 'local', 'cloudinary', 'aws-s3')"}, "provider_metadata": {"type": ["object", "null"], "description": "Provider-specific metadata"}, "createdAt": {"type": "string", "format": "date-time", "description": "File creation timestamp"}, "updatedAt": {"type": "string", "format": "date-time", "description": "File last update timestamp"}, "publishedAt": {"type": ["string", "null"], "format": "date-time", "description": "File publication timestamp"}}, "required": ["id", "name", "url", "mime", "size", "hash", "ext"]}}, "totalUploaded": {"type": "integer", "description": "Number of files successfully uploaded", "minimum": 0}, "errors": {"type": "array", "description": "List of upload errors if any occurred", "items": {"type": "object", "properties": {"filename": {"type": "string", "description": "Name of the file that failed to upload"}, "error": {"type": "string", "description": "Error message describing what went wrong"}}, "required": ["filename", "error"]}}}, "required": ["uploadedFiles", "totalUploaded", "errors"]}, "annotations": null}, {"name": "strapi_rest", "description": "Execute REST API operations on Strapi", "input_schema": {"type": "object", "properties": {"server": {"type": "string", "description": "Server name"}, "endpoint": {"type": "string", "description": "API endpoint"}, "method": {"type": "string", "enum": ["GET", "POST", "PUT", "DELETE"]}, "data": {"type": "object", "description": "Request body data"}}, "required": ["server", "endpoint", "method"]}, "output_schema": {"type": "object", "properties": {"data": {"description": "Response data from Strapi API", "oneOf": [{"type": "object", "description": "Single entity response"}, {"type": "array", "description": "Multiple entities response", "items": {"type": "object"}}]}, "meta": {"type": "object", "description": "<PERSON><PERSON><PERSON> about the response", "properties": {"pagination": {"type": "object", "properties": {"page": {"type": "integer"}, "pageSize": {"type": "integer"}, "pageCount": {"type": "integer"}, "total": {"type": "integer"}}}}}, "method": {"type": "string", "enum": ["GET", "POST", "PUT", "DELETE"], "description": "HTTP method used"}, "endpoint": {"type": "string", "description": "API endpoint called"}, "statusCode": {"type": "integer", "description": "HTTP status code"}}, "required": ["data", "method", "endpoint", "statusCode"]}, "annotations": null}, {"name": "strapi_get_content_types", "description": "Get content type schemas from a Strapi server", "input_schema": {"type": "object", "properties": {"server": {"type": "string", "description": "Server name to query"}}, "required": ["server"]}, "output_schema": {"type": "object", "properties": {"contentTypes": {"type": "array", "description": "List of content types from Strapi", "items": {"type": "object", "properties": {"uid": {"type": "string", "description": "Unique identifier (e.g., api::article.article)"}, "apiID": {"type": "string", "description": "API identifier (e.g., article)"}, "displayName": {"type": "string", "description": "Human-readable name"}, "description": {"type": "string", "description": "Content type description"}, "kind": {"type": "string", "enum": ["collectionType", "singleType"], "description": "Type of content"}, "attributes": {"type": "object", "description": "Field definitions", "additionalProperties": {"type": "object", "properties": {"type": {"type": "string", "description": "Field type (string, text, number, etc.)"}, "required": {"type": "boolean", "description": "Whether field is required"}}}}}, "required": ["uid", "apiID", "displayName", "kind"]}}, "totalCount": {"type": "integer", "description": "Total number of content types"}, "serverName": {"type": "string", "description": "Name of the Strapi server"}}, "required": ["contentTypes", "totalCount"]}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": null, "git_user_name": null, "integrations": null, "url": null}}