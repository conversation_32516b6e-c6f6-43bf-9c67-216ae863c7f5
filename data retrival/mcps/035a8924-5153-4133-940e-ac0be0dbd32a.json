{"success": true, "message": "MCP details retrieved successfully", "mcp": {"id": "035a8924-5153-4133-940e-ac0be0dbd32a", "name": "DuckDuckGo", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/duckduckgo-icon.png/1750354914-duckduckgo-icon.png", "description": "A Model Context Protocol (MCP) server that provides web search capabilities through DuckDuckGo, with additional features for content fetching and parsing.", "category": "general", "tags": null, "created_at": "2025-06-16T14:23:17.829829", "updated_at": "2025-08-25T12:07:39.889607", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "hosted_url": null, "mcp_type": "stdio", "owner_name": "<PERSON><PERSON>", "average_rating": null, "visibility": "public", "git_url": "https://github.com/<PERSON><PERSON>-Patil-RI/duckduckgo-mcp-server.git", "api_documentation": null, "capabilities": ["search", "fetch_content"], "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "search", "description": "\n    Search DuckDuckGo and return formatted results.\n\n    Args:\n        query: The search query string\n        max_results: Maximum number of results to return (default: 10)\n        ctx: MCP context for logging\n    ", "input_schema": {"properties": {"query": {"title": "Query", "type": "string"}, "max_results": {"default": 10, "title": "Max Results", "type": "integer"}}, "required": ["query"], "title": "searchArguments", "type": "object"}, "output_schema": {"properties": {"test": {"type": "string", "description": "test", "title": "test"}}}, "annotations": null}, {"name": "fetch_content", "description": "\n    Fetch and parse content from a webpage URL.\n\n    Args:\n        url: The webpage URL to fetch content from\n        ctx: MCP context for logging\n    ", "input_schema": {"properties": {"url": {"title": "Url", "type": "string"}}, "required": ["url"], "title": "fetch_contentArguments", "type": "object"}, "output_schema": {"properties": {"test": {"type": "string", "description": "test", "title": "test"}}}, "annotations": null}]}, "department": null, "is_added": false, "component_category": null, "repo_name": "duckduckgo-mcp-server", "git_user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>-RI", "integrations": null, "url": null}}