{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "34a390ae-d969-43ea-a2e5-aad8e04e97a8", "name": "Google Sheets Row Range Reader v1", "description": "Google_Sheets_Row_Range_Reader_v1", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/149e5e76-0107-4872-bc5b-45182589e243.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/cca28a42-1118-4a84-84d0-3ef35d75fc25.json", "start_nodes": [{"field": "main_input", "type": "dict", "transition_id": "transition-MergeDataComponent-1753422353677"}], "owner_id": "4f19b076-b555-4d69-ba27-59dc10ecbf58", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 5, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-25T10:53:49.944171", "updated_at": "2025-08-22T04:52:44.647224", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1753422353677", "label": "Merge Data After Receiving Input"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753427589329", "label": "Select Range Key From Input"}, {"name": "MCP_Google_Sheets_get_values_in_range", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_get_values_in_range-1753423079288", "type": "mcp", "display_name": "Google Sheets - get_values_in_range", "label": "Google Sheets - get_values_in_range", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "range_notation": {"title": "Range Notation", "type": "string"}, "value_render_option": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": "FORMATTED_VALUE", "title": "Value Render Option"}}, "required": ["spreadsheet_id", "range_notation"], "title": "GetValuesInRange", "type": "object"}, "output_schema": {}}}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1753445532865", "label": "Universal Converter"}], "source_workflow_id": "97ef85bd-7784-421a-8af2-52aeb5b7435c", "source_version_id": "f3f97e59-8263-432e-9a79-c7b9a7aca7d6", "has_updates": false, "current_version_id": "f3f97e59-8263-432e-9a79-c7b9a7aca7d6"}}