{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "4a1ba435-cb33-4e3b-9539-9b40ae30aca6", "name": "Company Research Workflow", "description": "Company_Research_Workflow", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/3d4f756a-ebdf-4e88-9c12-dc9185c8d94e.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/985e1f6f-22b5-4e4a-a44f-cb8df9424b5d.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1752768337944"}], "owner_id": "d3b78018-fe5a-4dd4-8148-d533ab3d8a93", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 4, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-18T07:53:40.739024", "updated_at": "2025-08-22T04:57:25.917929", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752768337944", "label": "AI Agent Executor"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752770571139", "label": "AI Agent Executor"}], "source_workflow_id": "d3ec861d-da29-4e5e-8c8e-db480d15d5cd", "source_version_id": "d5ba6c53-53ec-4dce-b73e-236b6e968a58", "has_updates": false, "current_version_id": "d5ba6c53-53ec-4dce-b73e-236b6e968a58"}}