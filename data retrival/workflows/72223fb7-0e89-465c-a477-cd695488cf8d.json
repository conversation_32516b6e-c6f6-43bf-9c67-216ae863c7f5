{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "72223fb7-0e89-465c-a477-cd695488cf8d", "name": "Update Job Status", "description": "Update_Job_Status", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/ac1e17a7-5b00-422a-b583-c6fe3d8b7203.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/c9afd89e-abdb-4bc0-abff-078a86caa3d6.json", "start_nodes": [{"field": "search_value", "type": "string", "transition_id": "transition-MCP_Google_Sheets_find_row-1753463636173"}, {"field": "main_input", "type": "string", "transition_id": "transition-CombineTextComponent-1753778938530"}], "owner_id": "55b1d790-e922-40ec-9da9-7f784894ab42", "owner_name": "<PERSON><PERSON>", "use_count": 4, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-31T08:09:33.759683", "updated_at": "2025-09-01T13:00:45.300185", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1753778938530", "label": "Status"}, {"name": "MCP_Google_Sheets_find_row", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_find_row-1753463636173", "type": "mcp", "display_name": "Google Sheets - find_row", "label": "Google Sheets - find_row", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "search_column": {"title": "Search Column", "type": "string"}, "search_value": {"title": "Search Value", "type": "string"}, "return_first_match": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Return First Match"}}, "required": ["spreadsheet_id", "search_column", "search_value"], "title": "FindRow", "type": "object"}, "output_schema": {}}}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1753778980428", "label": "Combine Text"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753680118871", "label": "Select Data"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1753680163318", "label": "Combine Text"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753778682862", "label": "AI Agent Executor"}, {"name": "MCP_Google_Sheets_update_cell", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_update_cell-1753463412537", "type": "mcp", "display_name": "Google Sheets - update_cell", "label": "Google Sheets - update_cell", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}, "value": {"title": "Value", "type": "string"}}, "required": ["spreadsheet_id", "cell", "value"], "title": "UpdateCell", "type": "object"}, "output_schema": {}}}], "source_workflow_id": "8721ac6b-a062-4bb0-848c-1a4bf876386c", "source_version_id": "c3493f7a-d213-4066-8f82-26bfc2bfe812", "has_updates": false, "current_version_id": "c3493f7a-d213-4066-8f82-26bfc2bfe812"}}