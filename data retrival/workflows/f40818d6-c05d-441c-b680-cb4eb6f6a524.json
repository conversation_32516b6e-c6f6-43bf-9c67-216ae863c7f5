{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "f40818d6-c05d-441c-b680-cb4eb6f6a524", "name": "JD By ID", "description": "JD_By_ID", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/95ed67fd-adce-4a03-b956-5a29a179f488.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/a2083e27-ed8f-459e-ada3-d0d1d156a260.json", "start_nodes": [{"field": "search_value", "type": "string", "transition_id": "transition-MCP_Google_Sheets_find_row-1753432277498"}], "owner_id": "55b1d790-e922-40ec-9da9-7f784894ab42", "owner_name": "<PERSON><PERSON>", "use_count": 3, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-31T08:09:43.364676", "updated_at": "2025-08-22T04:50:08.348322", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MCP_Google_Sheets_find_row", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_find_row-1753432277498", "type": "mcp", "display_name": "Google Sheets - find_row", "label": "Google Sheets - find_row", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "search_column": {"title": "Search Column", "type": "string"}, "search_value": {"title": "Search Value", "type": "string"}, "return_first_match": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Return First Match"}}, "required": ["spreadsheet_id", "search_column", "search_value"], "title": "FindRow", "type": "object"}, "output_schema": {}}}], "source_workflow_id": "76d2c5f7-546a-4a41-a9af-a989fc73a3cd", "source_version_id": "8e7a1183-00b2-4587-85df-84f4405a918e", "has_updates": false, "current_version_id": "8e7a1183-00b2-4587-85df-84f4405a918e"}}