{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "ea33e0c3-cf5e-40b3-8865-7b0dce77e045", "name": "Resume Scorer II", "description": "Resume_Scorer_II", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/627bc480-ff73-4ee7-8988-f96331fbaa7b.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/a7d6670e-1ab0-436c-a82c-6d195024cb47.json", "start_nodes": [{"field": "main_input", "type": "string", "transition_id": "transition-CombineTextComponent-1753416127009"}], "owner_id": "55b1d790-e922-40ec-9da9-7f784894ab42", "owner_name": "<PERSON><PERSON>", "use_count": 7, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.5.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-23T13:09:01.973921", "updated_at": "2025-09-01T13:17:55.709894", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1753416127009", "label": "JobID"}, {"name": "MCP_Google_Sheets_find_row", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-vGYizJTKz41RvgztFAuvQ", "type": "mcp", "display_name": "Google Sheets - find_row", "label": "Google Sheets - find_row (Copy)", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "search_column": {"title": "Search Column", "type": "string"}, "search_value": {"title": "Search Value", "type": "string"}, "return_first_match": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Return First Match"}}, "required": ["spreadsheet_id", "search_column", "search_value"], "title": "FindRow", "type": "object"}, "output_schema": {}}}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1753789805022", "label": "Combine Text"}, {"name": "MCP_Google_Sheets_find_row", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_find_row-1753854640984", "type": "mcp", "display_name": "Google Sheets - find_row", "label": "Google Sheets - find_row", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "search_column": {"title": "Search Column", "type": "string"}, "search_value": {"title": "Search Value", "type": "string"}, "return_first_match": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Return First Match"}}, "required": ["spreadsheet_id", "search_column", "search_value"], "title": "FindRow", "type": "object"}, "output_schema": {}}}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753254661190", "label": "Format Responses Using Agent"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1753254834834", "label": "JSONized Responses"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753256020252", "label": "Response Data"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753855767028", "label": "Row Index"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-rrhZzmhDBmO6seH9uYcj6", "label": "Status"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-ZcElckqRof5gr296qdhPz", "label": "Summary"}, {"name": "MCP_PDF_Reader_extract_metadata", "id": "45558ef3-6d37-42a6-9a91-b2995d6b3e1e", "transition_id": "transition-MCP_PDF_Reader_extract_metadata-1753265721134", "type": "mcp", "display_name": "PDF Reader - extract_metadata", "label": "PDF Reader - extract_metadata", "data": {"input_schema": {"type": "object", "properties": {"file_url": {"type": "string", "description": "Public URL of the file to extract metadata from"}}, "required": ["file_url"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "File Extraction Response", "type": "object", "required": ["success", "source", "file_url", "file_name", "format", "file_info"], "properties": {"success": {"type": "boolean"}, "source": {"type": "string", "enum": ["url", "upload", "other"]}, "file_url": {"type": "string", "format": "uri"}, "file_name": {"type": "string"}, "format": {"type": "string", "enum": ["pdf", "docx", "doc", "ppt", "xls", "txt", "html"]}, "file_info": {"type": "object", "required": ["size", "mime_type", "pages", "last_modified"], "properties": {"size": {"type": "integer", "minimum": 0}, "mime_type": {"type": "string", "format": "mime-type"}, "pages": {"type": "integer", "minimum": 1}, "last_modified": {"type": "string", "format": "date-time"}}}}}}}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1753856206950", "label": "Score"}, {"name": "MCP_PDF_Reader_extract_file_content", "id": "45558ef3-6d37-42a6-9a91-b2995d6b3e1e", "transition_id": "transition-MCP_PDF_Reader_extract_file_content-1753265723018", "type": "mcp", "display_name": "PDF Reader - extract_file_content", "label": "PDF Reader - extract_file_content", "data": {"input_schema": {"type": "object", "properties": {"source": {"type": "string", "enum": ["url", "upload"], "description": "Source type: URL or local upload"}, "file_url": {"type": "string", "description": "Public URL of the file (required if source is 'url')"}, "file_data": {"type": "string", "description": "Base64 encoded file data (required if source is 'upload')"}, "file_name": {"type": "string", "description": "Original filename with extension"}, "format": {"type": "string", "enum": ["pdf", "doc", "docx", "csv", "xlsx", "pptx", "xls", "ppt"], "description": "File format"}, "limit": {"type": "number", "maximum": 1000, "default": 100, "description": "Number of rows to return (for CSV/XLSX only, ignored for PDF/DOC)"}, "offset": {"type": "number", "minimum": 0, "default": 0, "description": "Number of rows to skip (for CSV/XLSX only, ignored for PDF/DOC)"}, "sheet_name": {"type": "string", "description": "Specific sheet name for XLSX files (optional, defaults to first sheet)"}, "search_query": {"type": "string", "description": "Search query to filter rows in CSV/XLSX files (only applicable for spreadsheet formats)"}, "search_type": {"type": "string", "enum": ["full_text", "exact_match", "regex"], "default": "full_text", "description": "Type of search to perform in CSV/XLSX files (default: 'full_text')"}, "case_sensitive": {"type": "boolean", "default": false, "description": "Whether search should be case sensitive for CSV/XLSX files (default: false)"}}, "required": ["source", "file_name", "format"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}, "output_schema": {"type": "object", "properties": {"data": {"type": "string", "description": "data of the job", "title": "data"}}, "required": ["data"]}}}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1753415094110", "label": "Merge Data"}, {"name": "AlterMetadataComponent", "display_name": "<PERSON>er Metada<PERSON>", "type": "component", "transition_id": "transition-AlterMetadataComponent-1753424353123", "label": "<PERSON>er Metada<PERSON>"}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1753780853658", "label": "Job ID For agent"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1753266130406", "label": "Summarizer And Scorer"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1753856853613", "label": "Universal Converter"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-_AhcFPqjcCEjDUzDf-NTS", "label": "Select Data (Copy)"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-pTlBFWRLX7RIHHXMk08Cb", "label": "Select Data (Copy)"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753856601536", "label": "Select Data"}, {"name": "MCP_Google_Sheets_update_cell", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-H1BBRI2JI_ywBQmj8jIIo", "type": "mcp", "display_name": "Google Sheets - update_cell", "label": "Update Summary Cell", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}, "value": {"title": "Value", "type": "string"}}, "required": ["spreadsheet_id", "cell", "value"], "title": "UpdateCell", "type": "object"}, "output_schema": {}}}, {"name": "MCP_Google_Sheets_update_cell", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_update_cell-1753856423790", "type": "mcp", "display_name": "Google Sheets - update_cell", "label": "Update Score Cell", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}, "value": {"title": "Value", "type": "string"}}, "required": ["spreadsheet_id", "cell", "value"], "title": "UpdateCell", "type": "object"}, "output_schema": {}}}, {"name": "MCP_Google_Sheets_update_cell", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-3Ya7dBabyTnd82WzNsjcr", "type": "mcp", "display_name": "Google Sheets - update_cell", "label": "Update Status Cell", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "cell": {"title": "Cell", "type": "string"}, "value": {"title": "Value", "type": "string"}}, "required": ["spreadsheet_id", "cell", "value"], "title": "UpdateCell", "type": "object"}, "output_schema": {}}}], "source_workflow_id": "ca02ed94-d186-4eb1-a5ae-d7941ae09237", "source_version_id": "efea3163-576f-401a-a399-5abd859b7c30", "has_updates": false, "current_version_id": "efea3163-576f-401a-a399-5abd859b7c30"}}