{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "355a99de-e7ce-4741-bf29-bb1f37e52423", "name": "Agentic_component", "description": "Agentic_component", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/87eda95d-6e0d-4e79-b264-4e952652b982.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/8e7f9b24-c948-4e64-830e-116559e2827b.json", "start_nodes": [{"field": "main_input", "type": "string", "transition_id": "transition-CombineTextComponent-1750163464444"}, {"field": "jd_details", "type": "string", "transition_id": "transition-MCP_Candidate_Interview_generate_interview_agenda-1750170194752"}], "owner_id": "c1454e90-09ac-40f2-bde2-833387d7b645", "owner_name": "<PERSON><PERSON><PERSON>", "use_count": 12, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-06-17T13:35:19.976078", "updated_at": "2025-08-22T05:02:03.615495", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MCP_Candidate_Interview_candidate_suitability_pre", "id": "0447fd55-c8f5-4c65-b2c3-e768bd663b13", "transition_id": "transition-MCP_Candidate_Interview_candidate_suitability_pre-1750142586819", "type": "mcp", "display_name": "Candidate_Interview - candidate_suitability_pre", "data": {"input_schema": {"properties": {"resume_s3_link": {"description": "S3 link to the candidate's resume", "format": "uri", "maxLength": 2083, "minLength": 1, "title": "Resume S3 Link", "type": "string"}, "job_description_s3_link": {"description": "S3 link to the job description", "format": "uri", "maxLength": 2083, "minLength": 1, "title": "Job Description S3 Link", "type": "string"}}, "required": ["resume_s3_link", "job_description_s3_link"], "title": "CandidateSuitabilityPreSchema", "type": "object"}, "output_schema": {"properties": {"suitability_analysis": {"type": "string", "description": "Analysis of candidate's suitability for the job", "title": "suitability_analysis"}, "resume_details": {"type": "string", "description": "Candidate's resume", "title": "resume_details"}, "jd_details": {"type": "string", "description": "Interview job description", "title": "jd_details"}}}}}, {"name": "MCP_Candidate_Interview_generate_interview_agenda", "id": "0447fd55-c8f5-4c65-b2c3-e768bd663b13", "transition_id": "transition-MCP_Candidate_Interview_generate_interview_agenda-1750142594819", "type": "mcp", "display_name": "Candidate_Interview - generate_interview_agenda", "data": {"input_schema": {"properties": {"resume_details": {"description": " candidate's resume", "title": "Resume Details", "type": "string"}, "jd_details": {"description": "job description", "title": "Jd Details", "type": "string"}, "prompt": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Optional custom prompt to guide the agenda generation", "title": "Prompt"}}, "required": ["resume_details", "jd_details"], "title": "GenerateAgendaSchema", "type": "object"}, "output_schema": {"properties": {"interview_agenda": {"type": "string", "description": "Generated interview agenda", "title": "interview_agenda"}, "resume_details": {"type": "string", "description": "Candidate's resume", "title": "resume_details"}, "jd_details": {"type": "string", "description": "Interview job description", "title": "jd_details"}}}}}, {"name": "CombineTextComponent", "display_name": "Combine Text", "type": "component", "transition_id": "transition-CombineTextComponent-1750163464444"}, {"name": "MCP_Candidate_Interview_generate_interview_agenda", "id": "0447fd55-c8f5-4c65-b2c3-e768bd663b13", "transition_id": "transition-MCP_Candidate_Interview_generate_interview_agenda-1750170194752", "type": "mcp", "display_name": "Candidate_Interview - generate_interview_agenda", "data": {"input_schema": {"properties": {"resume_details": {"description": " candidate's resume", "title": "Resume Details", "type": "string"}, "jd_details": {"description": "job description", "title": "Jd Details", "type": "string"}, "prompt": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Optional custom prompt to guide the agenda generation", "title": "Prompt"}}, "required": ["resume_details", "jd_details"], "title": "GenerateAgendaSchema", "type": "object"}, "output_schema": {"properties": {"interview_agenda": {"type": "string", "description": "Generated interview agenda", "title": "interview_agenda"}, "resume_details": {"type": "string", "description": "Candidate's resume", "title": "resume_details"}, "jd_details": {"type": "string", "description": "Interview job description", "title": "jd_details"}}}}}], "source_workflow_id": "678b2800-05da-46b0-83dd-c516b562889a", "source_version_id": "2b17352e-a491-4cac-95d8-14221325a861", "has_updates": false, "current_version_id": "2b17352e-a491-4cac-95d8-14221325a861"}}