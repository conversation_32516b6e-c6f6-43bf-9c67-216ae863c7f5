{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "0143feae-68ee-4676-8729-1d4b71728dc7", "name": "Google Forms Responses To Sheets", "description": "Google_Forms_Responses_To_Sheets", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/89275ca0-9726-41c2-81fa-18eda7c566eb.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/5ab1a4a8-3bfb-40e3-8459-7dd0db83409d.json", "start_nodes": [{"field": "form_id", "type": "string", "transition_id": "transition-MCP_Google_Forms_get_google_form_responses-1753180185744"}, {"field": "title", "type": "string", "transition_id": "transition-MCP_Google_Sheets_create_worksheet-1753180229571"}, {"field": "worksheet_name", "type": "string", "transition_id": "transition-MCP_Google_Sheets_find_row-1753181883932"}], "owner_id": "598f7395-c39a-4bfa-9996-0e3bff89047c", "owner_name": "<PERSON><PERSON><PERSON><PERSON>", "use_count": 6, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-23T04:40:15.174550", "updated_at": "2025-08-22T04:54:34.980121", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MCP_Google_Forms_get_google_form_responses", "id": "d10c53b8-4224-4145-8f34-01633fe3e3ce", "transition_id": "transition-MCP_Google_Forms_get_google_form_responses-1753180185744", "type": "mcp", "display_name": "Google Forms - get_google_form_responses", "label": "Google Form Responses", "data": {"input_schema": {"properties": {"form_id": {"title": "Form Id", "type": "string"}}, "required": ["form_id"], "title": "GetGoogleFormResponses", "type": "object"}, "output_schema": {}}}, {"name": "MCP_Google_Sheets_create_worksheet", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_create_worksheet-1753180229571", "type": "mcp", "display_name": "Google Sheets - create_worksheet", "label": "Create New Worksheet", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "title": {"title": "Title", "type": "string"}, "row_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1000, "title": "Row Count"}, "column_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 26, "title": "Column Count"}}, "required": ["spreadsheet_id", "title"], "title": "CreateWorksheet", "type": "object"}, "output_schema": {}}}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1753181465723", "label": "Universal Converter"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753181693168", "label": "Select Responses"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-MQM7eZA1HaziFn8QbHPri", "label": "Select Response ID"}, {"name": "MCP_Google_Sheets_find_row", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_find_row-1753181883932", "type": "mcp", "display_name": "Google Sheets - find_row", "label": "Find Row Using response ID", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "search_column": {"title": "Search Column", "type": "string"}, "search_value": {"title": "Search Value", "type": "string"}, "return_first_match": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Return First Match"}}, "required": ["spreadsheet_id", "search_column", "search_value"], "title": "FindRow", "type": "object"}, "output_schema": {"type": "object", "properties": {"data": {"type": "array", "description": "list of values in that row", "title": "data"}}, "required": ["data"]}}}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-NYbP9VRM4QTBUDcZDLzAC", "label": "Select Rows"}, {"name": "ConditionalNode", "display_name": "Switch-Case Router", "type": "component", "transition_id": "transition-ConditionalNode-1753182119150", "label": "Check if Rows is Empty"}, {"name": "MergeDataComponent", "display_name": "Merge Data", "type": "component", "transition_id": "transition-MergeDataComponent-1753183431621", "label": "Answer Merge"}], "source_workflow_id": "b21aa490-414b-41e7-bf2e-6a41c860ef6a", "source_version_id": "3f2ed7a6-4bc6-4b54-8663-3adb4632d2d6", "has_updates": false, "current_version_id": "3f2ed7a6-4bc6-4b54-8663-3adb4632d2d6"}}