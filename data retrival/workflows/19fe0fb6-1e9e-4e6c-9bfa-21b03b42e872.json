{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "19fe0fb6-1e9e-4e6c-9bfa-21b03b42e872", "name": "Meeting Rescheduler", "description": "Meeting_Rescheduler", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/561810ed-a2ca-42ec-99d6-7f9465e8134c.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/30178eb8-d651-4082-ac97-b896e6cdc9b4.json", "start_nodes": [{"field": "worksheet_name", "type": "string", "transition_id": "transition-MCP_Google_Sheets_find_row-1754982994772"}], "owner_id": "55b1d790-e922-40ec-9da9-7f784894ab42", "owner_name": "<PERSON><PERSON>", "use_count": 1, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-09-01T09:57:59.795556", "updated_at": "2025-09-01T09:58:17.977527", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MCP_Google_Sheets_find_row", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_find_row-1754982994772", "type": "mcp", "display_name": "Google Sheets - find_row", "label": "Google Sheets - find_row", "integrations": ["87b72e0c-e890-4ef5-bccf-7c783c1fb2bc"], "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "search_column": {"title": "Search Column", "type": "string"}, "search_value": {"title": "Search Value", "type": "string"}, "return_first_match": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Return First Match"}}, "required": ["spreadsheet_id", "search_column", "search_value"], "title": "FindRow", "type": "object"}, "output_schema": {}}}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1754983012888", "label": "Select Data", "integrations": []}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1754983156543", "label": "AI Agent Executor"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1754983203226", "label": "Universal Converter", "integrations": []}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1754983236193", "label": "Select Data", "integrations": []}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-_pKXxp1wS2P1zgpT4L8OY", "label": "Select Data (Copy)", "integrations": []}, {"name": "MCP_Gmail_find_email", "id": "37db65ab-0586-434e-a58d-7ddc6d9a8beb", "transition_id": "transition-MCP_Gmail_find_email-1754983332496", "type": "mcp", "display_name": "Gmail - find_email", "label": "Gmail - find_email", "integrations": ["20cebfff-1435-4081-90df-90a149f41194"], "data": {"input_schema": {"properties": {"query": {"title": "Query", "type": "string"}, "max_results": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 10, "title": "Max Results"}}, "required": ["query"], "title": "FindEmail", "type": "object"}, "output_schema": null}}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1754983424498", "label": "AI Agent Executor"}], "source_workflow_id": "55296a69-2b2d-44ab-9e63-63f6eab8f042", "source_version_id": "c9621ee6-39e9-4cd8-bcbe-03141c5e120f", "has_updates": false, "current_version_id": "c9621ee6-39e9-4cd8-bcbe-03141c5e120f"}}