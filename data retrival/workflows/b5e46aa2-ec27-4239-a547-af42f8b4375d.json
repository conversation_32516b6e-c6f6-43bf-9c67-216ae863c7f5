{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "b5e46aa2-ec27-4239-a547-af42f8b4375d", "name": "JD Creation - (Agent-<PERSON><PERSON>) ", "description": "JD_Creation_-_(<PERSON><PERSON><PERSON><PERSON>)_", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/77228d33-d3a4-4c7a-814e-13b3e4a13b1b.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/da08c5de-2ae5-473a-9fe7-0c0e3acfdad8.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1752566127607"}], "owner_id": "598f7395-c39a-4bfa-9996-0e3bff89047c", "owner_name": "<PERSON><PERSON><PERSON><PERSON>", "use_count": 5, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.2.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-31T11:49:11.618973", "updated_at": "2025-09-01T10:06:31.617730", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752566127607", "label": "JD Writer – Agent"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-a5_AZcxrzQ6hHvOUg1B71", "label": "JD Manager - Agent (Copy)"}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-UniversalConverterComponent-1753793379040", "label": "Universal Converter"}, {"name": "MCP_Google_Sheets_add_single_row", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-HXFReGQWh5ORhVlhRhMLB", "type": "mcp", "display_name": "Google Sheets - add_single_row", "label": "Google Sheets - add_single_row (Copy)", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "row": {"items": {"type": "string"}, "title": "Row", "type": "array"}, "row_index": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Row Index"}}, "required": ["spreadsheet_id", "row"], "title": "AddSingleRow", "type": "object"}, "output_schema": {}}}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753793512452", "label": "Select Data"}, {"name": "MCP_Google_Sheets_find_row", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-ZlRs_2okFSPjA4GzLxfmB", "type": "mcp", "display_name": "Google Sheets - find_row", "label": "Google Sheets - find_row (Copy)", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "search_column": {"title": "Search Column", "type": "string"}, "search_value": {"title": "Search Value", "type": "string"}, "return_first_match": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Return First Match"}}, "required": ["spreadsheet_id", "search_column", "search_value"], "title": "FindRow", "type": "object"}, "output_schema": {}}}, {"name": "MCP_Google_Sheets_create_worksheet", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_create_worksheet-1753352590260", "type": "mcp", "display_name": "Google Sheets - create_worksheet", "label": "Google Sheets - create_worksheet", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "title": {"title": "Title", "type": "string"}, "row_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1000, "title": "Row Count"}, "column_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 26, "title": "Column Count"}}, "required": ["spreadsheet_id", "title"], "title": "CreateWorksheet", "type": "object"}, "output_schema": {}}}, {"name": "UniversalConverterComponent", "display_name": "Universal Converter", "type": "component", "transition_id": "transition-d7YCQEfNerRrErNIwAgvt", "label": "Universal Converter (Copy) (Copy)"}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1753352871716", "label": "Select Data"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-h3yXrB_9SaN5DMopcBA4m", "label": "JD Doc Creator - Agent (Copy)"}, {"name": "MCP_Google_Sheets_add_single_row", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_add_single_row-1753352637576", "type": "mcp", "display_name": "Google Sheets - add_single_row", "label": "Google Sheets - add_single_row", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "row": {"items": {"type": "string"}, "title": "Row", "type": "array"}, "row_index": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Row Index"}}, "required": ["spreadsheet_id", "row"], "title": "AddSingleRow", "type": "object"}, "output_schema": {}}}], "source_workflow_id": "c5f5d6d6-ea78-4973-b2fb-0f47abda0d69", "source_version_id": "f02d6359-9642-479b-ac17-c60458991d62", "has_updates": false, "current_version_id": "f02d6359-9642-479b-ac17-c60458991d62"}}