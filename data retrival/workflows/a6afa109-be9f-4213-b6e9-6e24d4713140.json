{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "a6afa109-be9f-4213-b6e9-6e24d4713140", "name": "Meeting Scheduler", "description": "Meeting_Scheduler", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/b4567a84-7619-4e06-af9d-aed98a2621da.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/f89e3f3e-2340-4725-8806-602418f03285.json", "start_nodes": [{"field": "worksheet_name", "type": "string", "transition_id": "transition-MCP_Google_Sheets_find_row-1754475554875"}], "owner_id": "55b1d790-e922-40ec-9da9-7f784894ab42", "owner_name": "<PERSON><PERSON>", "use_count": 1, "execution_count": 0, "average_rating": null, "category": null, "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-09-01T09:59:09.939231", "updated_at": "2025-09-01T09:59:35.359743", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "MCP_Google_Sheets_find_row", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "transition_id": "transition-MCP_Google_Sheets_find_row-1754475554875", "type": "mcp", "display_name": "Google Sheets - find_row", "label": "Google Sheets - find_row", "data": {"input_schema": {"properties": {"spreadsheet_id": {"title": "Spreadsheet Id", "type": "string"}, "worksheet_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Worksheet Name"}, "search_column": {"title": "Search Column", "type": "string"}, "search_value": {"title": "Search Value", "type": "string"}, "return_first_match": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Return First Match"}}, "required": ["spreadsheet_id", "search_column", "search_value"], "title": "FindRow", "type": "object"}, "output_schema": {}}}, {"name": "SelectDataComponent", "display_name": "Select Data", "type": "component", "transition_id": "transition-SelectDataComponent-1754476126385", "label": "Select Data"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1754477877986", "label": "AI Agent Executor"}], "source_workflow_id": "0b3e7c8c-6b17-4370-abc1-72969c2555e9", "source_version_id": "0ed92edd-d428-43b4-99dd-da8aee4f421a", "has_updates": false, "current_version_id": "0ed92edd-d428-43b4-99dd-da8aee4f421a"}}