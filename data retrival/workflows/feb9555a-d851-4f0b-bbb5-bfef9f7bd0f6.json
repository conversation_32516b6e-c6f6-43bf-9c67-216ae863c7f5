{"success": true, "message": "Workflow details retrieved successfully", "workflow": {"id": "feb9555a-d851-4f0b-bbb5-bfef9f7bd0f6", "name": "Marketing-Performance-Report-Generators", "description": "Marketing-Performance-Report-Generators", "image_url": null, "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/ac9007dc-c243-4432-a032-f4e59072950e.json", "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/c0907a23-4d2f-4896-bcad-a3b9d5dc82ff.json", "start_nodes": [{"field": "query", "type": "string", "transition_id": "transition-AgenticAI-1752733978545"}, {"field": "input_variables", "type": "dict", "transition_id": "transition-AgenticAI-1752734214106"}], "owner_id": "2051be58-0123-407f-892f-cbe74966f0ab", "owner_name": "<PERSON><PERSON><PERSON> <PERSON>h", "use_count": 4, "execution_count": 0, "average_rating": null, "category": "automation", "tags": null, "version": "1.1.0", "status": "active", "visibility": "PUBLIC", "created_at": "2025-07-17T06:56:44.218135", "updated_at": "2025-08-25T07:09:14.510888", "workflow_definition": null, "workflow_steps": null, "is_added": false, "available_nodes": [{"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752733978545", "label": "AI Agent Executor"}, {"name": "AgenticAI", "display_name": "AI Agent Executor", "type": "agent", "transition_id": "transition-AgenticAI-1752734214106", "label": "AI Agent Executor"}], "source_workflow_id": "2e2c963c-6140-4962-9882-c21ab2e90732", "source_version_id": "45c61739-17c9-4e82-a0d1-a5d874b99c97", "has_updates": false, "current_version_id": "45c61739-17c9-4e82-a0d1-a5d874b99c97"}}