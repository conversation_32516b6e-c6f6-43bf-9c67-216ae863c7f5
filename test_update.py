#!/usr/bin/env python3
"""
Test script for the optimized update.py module
"""

import os
import sys
import logging
from unittest.mock import Mock, patch, MagicMock
import pytest

# Add the current directory to the path so we can import update
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Mock environment variables before importing update
os.environ['PINECONE_API_KEY'] = 'test-api-key'

# Mock external dependencies
with patch('pinecone.Pinecone'), \
     patch('sentence_transformers.SentenceTransformer'):
    import update


def test_config_initialization():
    """Test that configuration is properly initialized"""
    assert update.config.PAGE_SIZE == 10
    assert update.config.REQUEST_TIMEOUT == 30
    assert update.config.MAX_RETRIES == 3
    assert "rapidinnovation.dev" in update.config.COMPONENT_URL


def test_get_embedding():
    """Test embedding generation function"""
    # Mock the embedding model
    with patch.object(update.embedding_model, 'encode') as mock_encode:
        mock_encode.return_value.tolist.return_value = [0.1, 0.2, 0.3]
        
        test_data = {
            "name": "test_component",
            "description": "A test component",
            "type": "component",
            "category": "test"
        }
        
        result = update.get_embedding(test_data)
        
        assert result == [0.1, 0.2, 0.3]
        mock_encode.assert_called_once()


def test_safe_api_request_success():
    """Test successful API request"""
    mock_response = Mock()
    mock_response.json.return_value = {"status": "success"}
    mock_response.raise_for_status.return_value = None
    
    with patch.object(update.session, 'get', return_value=mock_response):
        result = update.safe_api_request("http://test.com")
        
        assert result == {"status": "success"}


def test_safe_api_request_failure():
    """Test failed API request"""
    with patch.object(update.session, 'get', side_effect=Exception("Network error")):
        result = update.safe_api_request("http://test.com")
        
        assert result is None


def test_update_element_new_component():
    """Test updating a new component element"""
    test_data = {
        "type": "component",
        "name": "test_component",
        "description": "A test component",
        "category": "test"
    }
    
    # Mock Pinecone index
    mock_result = {"vectors": {}}
    
    with patch.object(update.index, 'fetch', return_value=mock_result), \
         patch.object(update.index, 'upsert') as mock_upsert, \
         patch.object(update, 'get_embedding', return_value=[0.1, 0.2, 0.3]):
        
        result = update.update_element(test_data)
        
        assert result is True
        mock_upsert.assert_called_once()


def test_logging_configuration():
    """Test that logging is properly configured"""
    # Check that logger exists and has handlers
    assert update.logger is not None
    assert len(update.logger.handlers) >= 0  # May have inherited handlers
    
    # Check log level
    root_logger = logging.getLogger()
    assert root_logger.level <= logging.INFO


def run_basic_functionality_test():
    """Run a basic test to ensure the module can be imported and basic functions work"""
    print("Testing basic functionality...")
    
    # Test configuration
    print(f"✓ Config loaded: PAGE_SIZE={update.config.PAGE_SIZE}")
    
    # Test session creation
    session = update.create_session()
    print(f"✓ Session created: {type(session).__name__}")
    
    # Test logging
    update.logger.info("Test log message")
    print("✓ Logging works")
    
    print("All basic functionality tests passed!")


if __name__ == "__main__":
    # Set up test logging
    logging.basicConfig(level=logging.INFO)
    
    try:
        run_basic_functionality_test()
        print("\n" + "="*50)
        print("BASIC TESTS COMPLETED SUCCESSFULLY")
        print("="*50)
        print("\nTo run full tests with pytest:")
        print("pip install pytest pytest-mock")
        print("pytest test_update.py -v")
        
    except Exception as e:
        print(f"Test failed: {e}")
        sys.exit(1)
