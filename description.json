[{"type": "component", "category": "AI", "name": "AgenticAI", "description": "Executes an AI agent with tools and memory using AutoGen."}, {"type": "component", "category": "Logic", "name": "ConditionalNode", "description": "Evaluates multiple conditions and routes data to matching outputs"}, {"type": "component", "category": "Logic", "name": "LoopNode", "description": "Iterates over a list, with advanced controls for parallelism, aggregation, and error handling."}, {"type": "component", "category": "Data Interaction", "name": "ApiRequestNode", "description": "Makes a single HTTP request to the specified URL."}, {"type": "component", "category": "Helpers", "name": "IDGeneratorComponent", "description": "Generates various types of unique identifiers (UUID, timestamp, short ID)."}, {"type": "component", "category": "IO", "name": "StartNode", "description": "The starting point for all workflows. Only nodes connected to this node will be executed."}, {"type": "component", "category": "Processing", "name": "AlterMetadataComponent", "description": "Modifies metadata dictionary keys."}, {"type": "component", "category": "Processing", "name": "CombineTextComponent", "description": "Joins text inputs with a separator, supporting a variable number of inputs. Use __SPACE__, __TAB__, __NEWLINE__ for special characters."}, {"type": "component", "category": "Processing", "name": "DataToDataFrameComponent", "description": "Converts data to a Pandas DataFrame."}, {"type": "component", "category": "Processing", "name": "DelayComponent", "description": "Pauses the workflow execution for a set number of seconds."}, {"type": "component", "category": "Processing", "name": "MergeDataComponent", "description": "Combines multiple dictionaries or lists."}, {"type": "component", "category": "Processing", "name": "MessageToDataComponent", "description": "Extracts fields from a Message object."}, {"type": "component", "category": "Processing", "name": "RegexExtractorComponent", "description": "Extract data from text using regular expressions"}, {"type": "component", "category": "Processing", "name": "SelectDataComponent", "description": "Extracts elements from lists or dictionaries."}, {"type": "component", "category": "Processing", "name": "SplitTextComponent", "description": "Splits text into a list using a delimiter."}, {"type": "component", "category": "Processing", "name": "UniversalConverterComponent", "description": "Convert data between different types (JSON, CSV, String, Number, Boolean, etc.)"}, {"type": "mcp", "id": "33c074a7-1d4b-4c50-8676-f6a503b7e2ad", "mcp_name": "Flux ImageGen", "updated_at": "2025-08-26T06:52:47.973327", "category": "general", "name": "generateImageUrl", "description": "Generate an image URL from a text prompt"}, {"type": "mcp", "id": "33c074a7-1d4b-4c50-8676-f6a503b7e2ad", "mcp_name": "Flux ImageGen", "updated_at": "2025-08-26T06:52:47.973327", "category": "general", "name": "generateImage", "description": "Generate an image, return the base64-encoded data, and save to a file by default"}, {"type": "mcp", "id": "33c074a7-1d4b-4c50-8676-f6a503b7e2ad", "mcp_name": "Flux ImageGen", "updated_at": "2025-08-26T06:52:47.973327", "category": "general", "name": "listImageModels", "description": "List available image models"}, {"type": "mcp", "id": "9d749227-a133-4307-b991-d454545bccb1", "mcp_name": "video-script-generation", "updated_at": "2025-08-18T10:25:33.421937", "category": "general", "name": "video_script_generate", "description": "Generate a video script given a topic and video time (seconds)"}, {"type": "mcp", "id": "de313c10-d664-49e0-889e-e41e0230f704", "mcp_name": "stock-video-generation-mcp", "updated_at": "2025-08-19T13:34:36.669597", "category": "marketing", "name": "generate_stock_video", "description": "generate and find the stock video for the video"}, {"type": "mcp", "id": "de313c10-d664-49e0-889e-e41e0230f704", "mcp_name": "stock-video-generation-mcp", "updated_at": "2025-08-19T13:34:36.669597", "category": "marketing", "name": "generate_ai_stock_video", "description": "generate the ai stock video using the script"}, {"type": "mcp", "id": "de313c10-d664-49e0-889e-e41e0230f704", "mcp_name": "stock-video-generation-mcp", "updated_at": "2025-08-19T13:34:36.669597", "category": "marketing", "name": "fetch_stock_videos", "description": "fetch the stock videos from search terms"}, {"type": "mcp", "id": "e7d07250-b2f3-403b-8617-cc021ce2c2c4", "mcp_name": "Postgres", "updated_at": "2025-08-28T13:29:30.651750", "category": "engineering", "name": "pg_analyze_database", "description": "Analyze PostgreSQL database configuration and performance"}, {"type": "mcp", "id": "e7d07250-b2f3-403b-8617-cc021ce2c2c4", "mcp_name": "Postgres", "updated_at": "2025-08-28T13:29:30.651750", "category": "engineering", "name": "pg_debug_database", "description": "Debug common PostgreSQL issues"}, {"type": "mcp", "id": "e7d07250-b2f3-403b-8617-cc021ce2c2c4", "mcp_name": "Postgres", "updated_at": "2025-08-28T13:29:30.651750", "category": "engineering", "name": "pg_manage_schema", "description": "Manage PostgreSQL schema - get schema info, create/alter tables, manage enums. Examples: operation=\"get_info\" for table lists, operation=\"create_table\" with tableName and columns, operation=\"get_enums\" to list enums, operation=\"create_enum\" with enumName and values"}, {"type": "mcp", "id": "e7d07250-b2f3-403b-8617-cc021ce2c2c4", "mcp_name": "Postgres", "updated_at": "2025-08-28T13:29:30.651750", "category": "engineering", "name": "pg_manage_functions", "description": "Manage PostgreSQL functions - get, create, or drop functions with a single tool. Examples: operation=\"get\" to list functions, operation=\"create\" with functionName=\"test_func\", parameters=\"\" (empty for no params), returnType=\"TEXT\", functionBody=\"SELECT 'Hello'\""}, {"type": "mcp", "id": "e7d07250-b2f3-403b-8617-cc021ce2c2c4", "mcp_name": "Postgres", "updated_at": "2025-08-28T13:29:30.651750", "category": "engineering", "name": "pg_manage_triggers", "description": "Manage PostgreSQL triggers - get, create, drop, and enable/disable triggers. Examples: operation=\"get\" to list triggers, operation=\"create\" with triggerName, tableName, functionName, operation=\"drop\" with triggerName and tableName, operation=\"set_state\" with triggerName, tableName, enable"}, {"type": "mcp", "id": "e7d07250-b2f3-403b-8617-cc021ce2c2c4", "mcp_name": "Postgres", "updated_at": "2025-08-28T13:29:30.651750", "category": "engineering", "name": "pg_manage_indexes", "description": "Manage PostgreSQL indexes - get, create, drop, reindex, and analyze usage with a single tool. Examples: operation=\"get\" to list indexes, operation=\"create\" with indexName, tableName, columns, operation=\"analyze_usage\" for performance analysis"}, {"type": "mcp", "id": "e7d07250-b2f3-403b-8617-cc021ce2c2c4", "mcp_name": "Postgres", "updated_at": "2025-08-28T13:29:30.651750", "category": "engineering", "name": "pg_manage_constraints", "description": "Manage PostgreSQL constraints - get, create foreign keys, drop foreign keys, create constraints, drop constraints. Examples: operation=\"get\" to list constraints, operation=\"create_fk\" with constraintName, tableName, columnNames, referencedTable, referencedColumns"}, {"type": "mcp", "id": "e7d07250-b2f3-403b-8617-cc021ce2c2c4", "mcp_name": "Postgres", "updated_at": "2025-08-28T13:29:30.651750", "category": "engineering", "name": "pg_manage_rls", "description": "Manage PostgreSQL Row-Level Security - enable/disable RLS and manage policies. Examples: operation=\"enable\" with tableName=\"users\", operation=\"create_policy\" with tableName, policyName, using, check"}, {"type": "mcp", "id": "e7d07250-b2f3-403b-8617-cc021ce2c2c4", "mcp_name": "Postgres", "updated_at": "2025-08-28T13:29:30.651750", "category": "engineering", "name": "pg_manage_users", "description": "Manage PostgreSQL users and permissions - create, drop, alter users, grant/revoke permissions. Examples: operation=\"create\" with username=\"testuser\", operation=\"grant\" with username, permissions, target, targetType"}, {"type": "mcp", "id": "e7d07250-b2f3-403b-8617-cc021ce2c2c4", "mcp_name": "Postgres", "updated_at": "2025-08-28T13:29:30.651750", "category": "engineering", "name": "pg_manage_query", "description": "Manage PostgreSQL query analysis and performance - operation=\"explain\" for EXPLAIN plans, operation=\"get_slow_queries\" for slow query analysis, operation=\"get_stats\" for query statistics, operation=\"reset_stats\" for clearing statistics"}, {"type": "mcp", "id": "e7d07250-b2f3-403b-8617-cc021ce2c2c4", "mcp_name": "Postgres", "updated_at": "2025-08-28T13:29:30.651750", "category": "engineering", "name": "pg_execute_query", "description": "Execute SELECT queries and data retrieval operations - operation=\"select/count/exists\" with query and optional parameters. Examples: operation=\"select\", query=\"SELECT * FROM users WHERE created_at > $1\", parameters=[\"2024-01-01\"]"}, {"type": "mcp", "id": "e7d07250-b2f3-403b-8617-cc021ce2c2c4", "mcp_name": "Postgres", "updated_at": "2025-08-28T13:29:30.651750", "category": "engineering", "name": "pg_execute_mutation", "description": "Execute data modification operations (INSERT/UPDATE/DELETE/UPSERT) - operation=\"insert/update/delete/upsert\" with table and data. Examples: operation=\"insert\", table=\"users\", data={\"name\":\"<PERSON>\",\"email\":\"<EMAIL>\"}"}, {"type": "mcp", "id": "e7d07250-b2f3-403b-8617-cc021ce2c2c4", "mcp_name": "Postgres", "updated_at": "2025-08-28T13:29:30.651750", "category": "engineering", "name": "pg_execute_sql", "description": "Execute arbitrary SQL statements - sql=\"ANY_VALID_SQL\" with optional parameters and transaction support. Examples: sql=\"CREATE INDEX ...\", sql=\"WITH complex_cte AS (...) SELECT ...\", transactional=true"}, {"type": "mcp", "id": "e7d07250-b2f3-403b-8617-cc021ce2c2c4", "mcp_name": "Postgres", "updated_at": "2025-08-28T13:29:30.651750", "category": "engineering", "name": "pg_manage_comments", "description": "Manage PostgreSQL object comments - get, set, remove comments on tables, columns, functions, and other database objects. Examples: operation=\"get\" with objectType=\"table\", objectName=\"users\", operation=\"set\" with comment text, operation=\"bulk_get\" for discovery"}, {"type": "mcp", "id": "e7d07250-b2f3-403b-8617-cc021ce2c2c4", "mcp_name": "Postgres", "updated_at": "2025-08-28T13:29:30.651750", "category": "engineering", "name": "pg_export_table_data", "description": "Export table data to JSON or CSV format"}, {"type": "mcp", "id": "e7d07250-b2f3-403b-8617-cc021ce2c2c4", "mcp_name": "Postgres", "updated_at": "2025-08-28T13:29:30.651750", "category": "engineering", "name": "pg_import_table_data", "description": "Import data from JSON or CSV file into a table"}, {"type": "mcp", "id": "e7d07250-b2f3-403b-8617-cc021ce2c2c4", "mcp_name": "Postgres", "updated_at": "2025-08-28T13:29:30.651750", "category": "engineering", "name": "pg_copy_between_databases", "description": "Copy data between two databases"}, {"type": "mcp", "id": "e7d07250-b2f3-403b-8617-cc021ce2c2c4", "mcp_name": "Postgres", "updated_at": "2025-08-28T13:29:30.651750", "category": "engineering", "name": "pg_monitor_database", "description": "Get real-time monitoring information for a PostgreSQL database"}, {"type": "mcp", "id": "56dfe8af-e982-4351-a669-0a03755b8c99", "mcp_name": "video-generation-mcp", "updated_at": "2025-08-27T13:13:11.089419", "category": "marketing", "name": "generate_video", "description": "generate and process the video"}, {"type": "mcp", "id": "a4dbc53f-af77-416f-9849-c82f0411695b", "mcp_name": "Webflow", "updated_at": "2025-07-10T07:53:15.635198", "category": "general", "name": "collection_fields_create_option", "description": null}, {"type": "mcp", "id": "a4dbc53f-af77-416f-9849-c82f0411695b", "mcp_name": "Webflow", "updated_at": "2025-07-10T07:53:15.635198", "category": "general", "name": "collection_fields_create_reference", "description": null}, {"type": "mcp", "id": "a4dbc53f-af77-416f-9849-c82f0411695b", "mcp_name": "Webflow", "updated_at": "2025-07-10T07:53:15.635198", "category": "general", "name": "collection_fields_update", "description": null}, {"type": "mcp", "id": "a4dbc53f-af77-416f-9849-c82f0411695b", "mcp_name": "Webflow", "updated_at": "2025-07-10T07:53:15.635198", "category": "general", "name": "collections_items_create_item_live", "description": null}, {"type": "mcp", "id": "a4dbc53f-af77-416f-9849-c82f0411695b", "mcp_name": "Webflow", "updated_at": "2025-07-10T07:53:15.635198", "category": "general", "name": "collections_items_update_items_live", "description": null}, {"type": "mcp", "id": "a4dbc53f-af77-416f-9849-c82f0411695b", "mcp_name": "Webflow", "updated_at": "2025-07-10T07:53:15.635198", "category": "general", "name": "collections_items_list_items", "description": null}, {"type": "mcp", "id": "a4dbc53f-af77-416f-9849-c82f0411695b", "mcp_name": "Webflow", "updated_at": "2025-07-10T07:53:15.635198", "category": "general", "name": "collections_items_create_item", "description": null}, {"type": "mcp", "id": "a4dbc53f-af77-416f-9849-c82f0411695b", "mcp_name": "Webflow", "updated_at": "2025-07-10T07:53:15.635198", "category": "general", "name": "collections_items_update_items", "description": null}, {"type": "mcp", "id": "a4dbc53f-af77-416f-9849-c82f0411695b", "mcp_name": "Webflow", "updated_at": "2025-07-10T07:53:15.635198", "category": "general", "name": "collections_items_publish_items", "description": null}, {"type": "mcp", "id": "a4dbc53f-af77-416f-9849-c82f0411695b", "mcp_name": "Webflow", "updated_at": "2025-07-10T07:53:15.635198", "category": "general", "name": "sites_list", "description": null}, {"type": "mcp", "id": "a4dbc53f-af77-416f-9849-c82f0411695b", "mcp_name": "Webflow", "updated_at": "2025-07-10T07:53:15.635198", "category": "general", "name": "sites_get", "description": null}, {"type": "mcp", "id": "a4dbc53f-af77-416f-9849-c82f0411695b", "mcp_name": "Webflow", "updated_at": "2025-07-10T07:53:15.635198", "category": "general", "name": "sites_publish", "description": null}, {"type": "mcp", "id": "a4dbc53f-af77-416f-9849-c82f0411695b", "mcp_name": "Webflow", "updated_at": "2025-07-10T07:53:15.635198", "category": "general", "name": "pages_list", "description": null}, {"type": "mcp", "id": "a4dbc53f-af77-416f-9849-c82f0411695b", "mcp_name": "Webflow", "updated_at": "2025-07-10T07:53:15.635198", "category": "general", "name": "pages_get_metadata", "description": null}, {"type": "mcp", "id": "a4dbc53f-af77-416f-9849-c82f0411695b", "mcp_name": "Webflow", "updated_at": "2025-07-10T07:53:15.635198", "category": "general", "name": "pages_update_page_settings", "description": null}, {"type": "mcp", "id": "a4dbc53f-af77-416f-9849-c82f0411695b", "mcp_name": "Webflow", "updated_at": "2025-07-10T07:53:15.635198", "category": "general", "name": "pages_get_content", "description": null}, {"type": "mcp", "id": "a4dbc53f-af77-416f-9849-c82f0411695b", "mcp_name": "Webflow", "updated_at": "2025-07-10T07:53:15.635198", "category": "general", "name": "pages_update_static_content", "description": null}, {"type": "mcp", "id": "a4dbc53f-af77-416f-9849-c82f0411695b", "mcp_name": "Webflow", "updated_at": "2025-07-10T07:53:15.635198", "category": "general", "name": "collections_list", "description": null}, {"type": "mcp", "id": "a4dbc53f-af77-416f-9849-c82f0411695b", "mcp_name": "Webflow", "updated_at": "2025-07-10T07:53:15.635198", "category": "general", "name": "collections_get", "description": null}, {"type": "mcp", "id": "a4dbc53f-af77-416f-9849-c82f0411695b", "mcp_name": "Webflow", "updated_at": "2025-07-10T07:53:15.635198", "category": "general", "name": "collections_create", "description": null}, {"type": "mcp", "id": "a4dbc53f-af77-416f-9849-c82f0411695b", "mcp_name": "Webflow", "updated_at": "2025-07-10T07:53:15.635198", "category": "general", "name": "collection_fields_create_static", "description": null}, {"type": "mcp", "id": "0e525463-1394-4baa-8c52-c8346de1f484", "mcp_name": "cal.com", "updated_at": "2025-08-18T16:31:17.766620", "category": "general", "name": "list_bookings", "description": "Get all Cal.com bookings for a specified time range. Returns a concise list with essential booking information."}, {"type": "mcp", "id": "0e525463-1394-4baa-8c52-c8346de1f484", "mcp_name": "cal.com", "updated_at": "2025-08-18T16:31:17.766620", "category": "general", "name": "create_booking", "description": "Create a new booking in Cal.com. Requires event type ID, start time, attendee details, and timezone."}, {"type": "mcp", "id": "0e525463-1394-4baa-8c52-c8346de1f484", "mcp_name": "cal.com", "updated_at": "2025-08-18T16:31:17.766620", "category": "general", "name": "list_event_types", "description": "Get all available Cal.com event types with their IDs, names, and durations"}, {"type": "mcp", "id": "0e525463-1394-4baa-8c52-c8346de1f484", "mcp_name": "cal.com", "updated_at": "2025-08-18T16:31:17.766620", "category": "general", "name": "check_availability", "description": "Check available time slots for a Cal.com event type on a specific date"}, {"type": "mcp", "id": "4fd76366-bd5b-4f10-bd23-276b4875ec67", "mcp_name": "Cal.com Testing Only", "updated_at": "2025-07-23T08:38:54.121541", "category": "general", "name": "list_bookings", "description": "Get all Cal.com bookings for a specified time range. Returns a concise list with essential booking information."}, {"type": "mcp", "id": "4fd76366-bd5b-4f10-bd23-276b4875ec67", "mcp_name": "Cal.com Testing Only", "updated_at": "2025-07-23T08:38:54.121541", "category": "general", "name": "create_booking", "description": "Create a new booking in Cal.com. Requires event type ID, start time, attendee details, and timezone."}, {"type": "mcp", "id": "4fd76366-bd5b-4f10-bd23-276b4875ec67", "mcp_name": "Cal.com Testing Only", "updated_at": "2025-07-23T08:38:54.121541", "category": "general", "name": "list_event_types", "description": "Get all available Cal.com event types with their IDs, names, and durations"}, {"type": "mcp", "id": "4fd76366-bd5b-4f10-bd23-276b4875ec67", "mcp_name": "Cal.com Testing Only", "updated_at": "2025-07-23T08:38:54.121541", "category": "general", "name": "check_availability", "description": "Check available time slots for a Cal.com event type on a specific date"}, {"type": "mcp", "id": "d2a7709e-63c0-46fc-bd2b-d91c5a3905a2", "mcp_name": "SlideSpeak", "updated_at": "2025-07-23T05:19:34.991011", "category": "marketing", "name": "generate_powerpoint", "description": "Generate a PowerPoint presentation based on text, length, and template using SlideSpeak"}, {"type": "mcp", "id": "d2a7709e-63c0-46fc-bd2b-d91c5a3905a2", "mcp_name": "SlideSpeak", "updated_at": "2025-07-23T05:19:34.991011", "category": "marketing", "name": "get_available_templates", "description": "Get all available presentation templates from SlideSpeak"}, {"type": "mcp", "id": "d2a7709e-63c0-46fc-bd2b-d91c5a3905a2", "mcp_name": "SlideSpeak", "updated_at": "2025-07-23T05:19:34.991011", "category": "marketing", "name": "generate_powerpoint_slide_by_slide", "description": "Generate a PowerPoint presentation slide by slide based on slides array and template using SlideSpeak"}, {"type": "mcp", "id": "068600be-4d02-4c06-a7f1-513d060cbfab", "mcp_name": "voice-generation-mcp", "updated_at": "2025-08-04T05:16:40.251994", "category": "marketing", "name": "create_voices", "description": "Add"}, {"type": "mcp", "id": "068600be-4d02-4c06-a7f1-513d060cbfab", "mcp_name": "voice-generation-mcp", "updated_at": "2025-08-04T05:16:40.251994", "category": "marketing", "name": "generate_audio", "description": "Generate video audio using the script"}, {"type": "mcp", "id": "068600be-4d02-4c06-a7f1-513d060cbfab", "mcp_name": "voice-generation-mcp", "updated_at": "2025-08-04T05:16:40.251994", "category": "marketing", "name": "fetch_audio", "description": "Fetch audio generated files links using ids"}, {"type": "mcp", "id": "f74dec20-df47-48e5-8fbc-f87c80a757aa", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-28T13:29:30.651778", "category": "general", "name": "send_sms", "description": "Send sms to the user using Twilio"}, {"type": "mcp", "id": "ac2252e5-24d4-4c86-b8fc-20f13f324ce5", "mcp_name": "Eraser", "updated_at": "2025-09-01T08:06:02.615753", "category": "general", "name": "generateDiagram", "description": "Generate a diagram from text description using Eraser API"}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "update_issue", "description": "Update fields and/or change the status of a Jira issue."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "add_comment_with_attachment", "description": "Add a comment with a file attachment to a Jira issue."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "create_issue", "description": "Create a new Jira issue."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "get_issue", "description": "Get a Jira issue by key."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "list_projects", "description": "List all Jira projects accessible to the user."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "delete_issue", "description": "Delete a Jira issue or subtask"}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "search_issues", "description": "Search for issues in a project using JQL"}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "attach_file", "description": "Attaches a file to an existing Jira issue from a local path or a URL."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "transition_issue", "description": "Transitions a Jira issue to a new status (e.g., 'In Progress', 'Done')."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "list_available_transitions", "description": "Lists all possible transition actions for a given Jira issue from its current status."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "add_comment", "description": "Add a comment to a Jira issue."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "attach_content", "description": "Creates a file from provided content and attaches it to a Jira issue."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "create_issue_link", "description": "Create a link between two existing Jira issues."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "get_user", "description": "Get a user's account ID by email address"}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "get_my_permissions", "description": "Checks the abilities of the current API key by listing the permissions of the user account that owns the key. This is how you check the 'scopes' or 'limits' of your API token."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "update_start_end_date_time", "description": "Update the start and end date of a Jira issue."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "add_worklog", "description": "Adds a worklog to a Jira issue."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "list_sprints", "description": "List all sprints for a given Jira board."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "update_sprint_goal", "description": "Update the goal of a sprint."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "list_boards", "description": "List all Jira boards accessible to the user."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "get_all_sprints", "description": "Get all sprints across all Jira boards accessible to the user."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "get_sprint_by_id", "description": "Get details of a specific Jira sprint by its ID."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "get_confluence_spaces", "description": "Get all Confluence spaces."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "get_confluence_page", "description": "Get a Confluence page by ID."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "create_confluence_page", "description": "Create a new Confluence page."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "confluence_search", "description": "Search Confluence content using simple terms or CQL (Confluence Query Language)."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "confluence_get_page_children", "description": "Get the list of child pages for a specific Confluence page."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "confluence_get_page_ancestors", "description": "Retrieve the ancestor (parent) pages of a specific Confluence page."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "confluence_get_comments", "description": "Get all comments associated with a specific Confluence page."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "confluence_update_page", "description": "Update the content or metadata of an existing Confluence page."}, {"type": "mcp", "id": "03f988dd-fbf0-4bf0-a263-e502fe892b92", "mcp_name": "Jira & Confluence", "updated_at": "2025-08-29T06:07:18.709037", "category": "general", "name": "confluence_delete_page", "description": "Delete a Confluence page permanently."}, {"type": "mcp", "id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "mcp_name": "Rapid HRMS", "updated_at": "2025-07-29T06:49:42.072406", "category": "general", "name": "leave_get_employee_leave_balance", "description": "Fetch leave balance for a specific employee. Optionally filter by leave type."}, {"type": "mcp", "id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "mcp_name": "Rapid HRMS", "updated_at": "2025-07-29T06:49:42.072406", "category": "general", "name": "leave_get_leave_types", "description": "Get all available leave types in the system"}, {"type": "mcp", "id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "mcp_name": "Rapid HRMS", "updated_at": "2025-07-29T06:49:42.072406", "category": "general", "name": "leave_search_employees", "description": "Search for employees by name or badge ID"}, {"type": "mcp", "id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "mcp_name": "Rapid HRMS", "updated_at": "2025-07-29T06:49:42.072406", "category": "general", "name": "leave_get_leave_requests", "description": "Get leave requests with optional filtering"}, {"type": "mcp", "id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "mcp_name": "Rapid HRMS", "updated_at": "2025-07-29T06:49:42.072406", "category": "general", "name": "leave_create_leave_request", "description": "Create a new leave request"}, {"type": "mcp", "id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "mcp_name": "Rapid HRMS", "updated_at": "2025-07-29T06:49:42.072406", "category": "general", "name": "attendance_get_employee_attendance", "description": "Get attendance records for a specific employee"}, {"type": "mcp", "id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "mcp_name": "Rapid HRMS", "updated_at": "2025-07-29T06:49:42.072406", "category": "general", "name": "attendance_get_attendance_summary", "description": "Get attendance summary for employees"}, {"type": "mcp", "id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "mcp_name": "Rapid HRMS", "updated_at": "2025-07-29T06:49:42.072406", "category": "general", "name": "attendance_clock_in_employee", "description": "Clock in an employee for attendance"}, {"type": "mcp", "id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "mcp_name": "Rapid HRMS", "updated_at": "2025-07-29T06:49:42.072406", "category": "general", "name": "attendance_clock_out_employee", "description": "Clock out an employee for attendance"}, {"type": "mcp", "id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "mcp_name": "Rapid HRMS", "updated_at": "2025-07-29T06:49:42.072406", "category": "general", "name": "attendance_get_overtime_records", "description": "Get overtime records for employees"}, {"type": "mcp", "id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "mcp_name": "Rapid HRMS", "updated_at": "2025-07-29T06:49:42.072406", "category": "general", "name": "attendance_get_late_early_records", "description": "Get late come and early out records"}, {"type": "mcp", "id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "mcp_name": "Rapid HRMS", "updated_at": "2025-07-29T06:49:42.072406", "category": "general", "name": "attendance_get_attendance_activities", "description": "Get attendance activities (clock in/out events)"}, {"type": "mcp", "id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "mcp_name": "Rapid HRMS", "updated_at": "2025-07-29T06:49:42.072406", "category": "general", "name": "finance_get_exchange_rates", "description": "Fetch current currency exchange rates"}, {"type": "mcp", "id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "mcp_name": "Rapid HRMS", "updated_at": "2025-07-29T06:49:42.072406", "category": "general", "name": "finance_generate_monthly_report", "description": "Generate finance monthly report"}, {"type": "mcp", "id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "mcp_name": "Rapid HRMS", "updated_at": "2025-07-29T06:49:42.072406", "category": "general", "name": "project_list_projects", "description": "List all active projects of the organization"}, {"type": "mcp", "id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "mcp_name": "Rapid HRMS", "updated_at": "2025-07-29T06:49:42.072406", "category": "general", "name": "project_filter_projects_by_balance", "description": "Filter projects by open balance amount with various comparison operators"}, {"type": "mcp", "id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "mcp_name": "Rapid HRMS", "updated_at": "2025-07-29T06:49:42.072406", "category": "general", "name": "project_fetch_my_projects", "description": "<PERSON><PERSON> projects where I am a team member."}, {"type": "mcp", "id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "mcp_name": "Rapid HRMS", "updated_at": "2025-07-29T06:49:42.072406", "category": "general", "name": "project_get_project_details", "description": "Fetch detailed information for a specific project by its name."}, {"type": "mcp", "id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "mcp_name": "Rapid HRMS", "updated_at": "2025-07-29T06:49:42.072406", "category": "general", "name": "project_assign_team_member", "description": "Assign a team member to a project by its name."}, {"type": "mcp", "id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "mcp_name": "Rapid HRMS", "updated_at": "2025-07-29T06:49:42.072406", "category": "general", "name": "project_get_project_team", "description": "Fetch the team members for a specific project by its name."}, {"type": "mcp", "id": "141a5cff-8e08-4ebb-aed1-0759bf212dfe", "mcp_name": "Rapid HRMS", "updated_at": "2025-07-29T06:49:42.072406", "category": "general", "name": "employee_fetch_employee_profile", "description": "Fetch public profile information for an employee by name. Defaults to your own profile if no name is provided."}, {"type": "mcp", "id": "1397e70d-e094-41bf-ad85-25b11a17f062", "mcp_name": "Google Drive", "updated_at": "2025-08-09T16:18:57.349587", "category": "general", "name": "find_file", "description": "Find files in Google Drive based on search query"}, {"type": "mcp", "id": "1397e70d-e094-41bf-ad85-25b11a17f062", "mcp_name": "Google Drive", "updated_at": "2025-08-09T16:18:57.349587", "category": "general", "name": "delete_file", "description": "Delete a file from Google Drive by its ID"}, {"type": "mcp", "id": "1397e70d-e094-41bf-ad85-25b11a17f062", "mcp_name": "Google Drive", "updated_at": "2025-08-09T16:18:57.349587", "category": "general", "name": "create_file", "description": "Create a new file in Google Drive with optional content"}, {"type": "mcp", "id": "748a8221-d7d9-4352-93ae-00700f4d28b1", "mcp_name": "script-generation-mcp", "updated_at": "2025-08-11T14:40:09.066178", "category": "marketing", "name": "script_generate", "description": "Provide topic and keyword to generator <PERSON><PERSON><PERSON>"}, {"type": "mcp", "id": "748a8221-d7d9-4352-93ae-00700f4d28b1", "mcp_name": "script-generation-mcp", "updated_at": "2025-08-11T14:40:09.066178", "category": "marketing", "name": "research", "description": "Research for the given topic"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "search_repositories", "description": "Search for GitHub repositories. Use 'scope' parameter to control search scope: 'global' (default) searches all GitHub repositories, 'user' searches only within the authenticated user's repositories. Returns a concise list with essential information. Use 'get_repository' for detailed information about a specific repository."}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "get_repository", "description": "Get detailed information about a GitHub repository including README and file structure"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "create_repository", "description": "Create a new GitHub repository in your account"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "fork_repository", "description": "Fork a GitHub repository to your account or specified organization"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "search_code", "description": "Search for code across GitHub repositories. Returns a concise list with file paths and repositories. Use 'get_file_contents' for full file content."}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "get_file_contents", "description": "Get the contents of a file from a GitHub repository"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "search_users", "description": "Search for GitHub users"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "get_issue", "description": "Get details of a specific issue in a GitHub repository"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "add_issue_comment", "description": "Add a comment to a specific issue in a GitHub repository"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "search_issues", "description": "Search for issues in GitHub repositories"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "create_issue", "description": "Create a new issue in a GitHub repository"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "list_issues", "description": "List issues in a GitHub repository"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "update_issue", "description": "Update an existing issue in a GitHub repository"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "get_issue_comments", "description": "Get comments for a specific issue in a GitHub repository"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "list_commits", "description": "Get list of commits of a branch in a GitHub repository"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "list_branches", "description": "List branches in a GitHub repository"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "create_branch", "description": "Create a new branch in a GitHub repository"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "list_tags", "description": "List git tags in a GitHub repository"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "get_tag", "description": "Get details about a specific git tag in a GitHub repository"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "get_pull_request", "description": "Get details of a specific pull request in a GitHub repository"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "update_pull_request", "description": "Update an existing pull request in a GitHub repository"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "list_pull_requests", "description": "List pull requests in a GitHub repository"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "merge_pull_request", "description": "Merge a pull request in a GitHub repository"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "get_pull_request_files", "description": "Get the files changed in a specific pull request"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "get_pull_request_status", "description": "Get the status of a specific pull request"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "update_pull_request_branch", "description": "Update the branch of a pull request with the latest changes from the base branch (not implemented)"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "get_pull_request_comments", "description": "Get comments for a specific pull request"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "create_pull_request", "description": "Create a new pull request in a GitHub repository"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "push_files", "description": "Push multiple files to a GitHub repository in a single commit"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "get_commit", "description": "Get details for a commit from a GitHub repository"}, {"type": "mcp", "id": "38e7f9f3-6429-48a3-b51d-b18991c9673c", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-29T18:20:47.606362", "category": "general", "name": "create_or_update_file", "description": "Create or update a single file in a GitHub repository. If updating an existing file, you must provide the current SHA of the file (the full 40-character SHA, not a shortened version)."}, {"type": "mcp", "id": "abd5ce5a-67aa-4a62-985c-d7ccc317e876", "mcp_name": "ZoHo CRM", "updated_at": "2025-08-15T08:16:32.199644", "category": "general", "name": "create_record", "description": "Create a new record in a Zoho CRM module. Required fields by module: Leads (Last_Name, Company), Contacts (Last_Name), Accounts (Account_Name), Deals (Deal_Name, Stage, Closing_Date)"}, {"type": "mcp", "id": "abd5ce5a-67aa-4a62-985c-d7ccc317e876", "mcp_name": "ZoHo CRM", "updated_at": "2025-08-15T08:16:32.199644", "category": "general", "name": "get_records", "description": "Retrieve records from Zoho CRM with optional field filtering and pagination"}, {"type": "mcp", "id": "abd5ce5a-67aa-4a62-985c-d7ccc317e876", "mcp_name": "ZoHo CRM", "updated_at": "2025-08-15T08:16:32.199644", "category": "general", "name": "search_records", "description": "Search records using Zoho-style criteria with advanced filtering"}, {"type": "mcp", "id": "abd5ce5a-67aa-4a62-985c-d7ccc317e876", "mcp_name": "ZoHo CRM", "updated_at": "2025-08-15T08:16:32.199644", "category": "general", "name": "health_check", "description": "Verify server health and Zoho connectivity"}, {"type": "mcp", "id": "abd5ce5a-67aa-4a62-985c-d7ccc317e876", "mcp_name": "ZoHo CRM", "updated_at": "2025-08-15T08:16:32.199644", "category": "general", "name": "get_tool_info", "description": "Return detailed parameter specs for any tool"}, {"type": "mcp", "id": "0931e5d9-fccc-459b-81a5-c1a251d16c7a", "mcp_name": "Google Document", "updated_at": "2025-08-15T08:17:26.874785", "category": "general", "name": "create_document", "description": "Create a new Google Document with optional title and content. Supports plain text, HTML, and Markdown formats."}, {"type": "mcp", "id": "0931e5d9-fccc-459b-81a5-c1a251d16c7a", "mcp_name": "Google Document", "updated_at": "2025-08-15T08:17:26.874785", "category": "general", "name": "update_document", "description": "Update a Google Document with new content at a specific position"}, {"type": "mcp", "id": "0931e5d9-fccc-459b-81a5-c1a251d16c7a", "mcp_name": "Google Document", "updated_at": "2025-08-15T08:17:26.874785", "category": "general", "name": "verify_connection", "description": "Verify the connection to Google Docs API and display user info"}, {"type": "mcp", "id": "0931e5d9-fccc-459b-81a5-c1a251d16c7a", "mcp_name": "Google Document", "updated_at": "2025-08-15T08:17:26.874785", "category": "general", "name": "append_document", "description": "Append content to the end of a Google Document"}, {"type": "mcp", "id": "0931e5d9-fccc-459b-81a5-c1a251d16c7a", "mcp_name": "Google Document", "updated_at": "2025-08-15T08:17:26.874785", "category": "general", "name": "insert_image", "description": "Insert an image into a Google Document at a specific position"}, {"type": "mcp", "id": "0931e5d9-fccc-459b-81a5-c1a251d16c7a", "mcp_name": "Google Document", "updated_at": "2025-08-15T08:17:26.874785", "category": "general", "name": "get_document", "description": "Retrieve the content of a Google Document by its ID"}, {"type": "mcp", "id": "3d94e0d9-73c4-4260-b11d-e600c9a1f35f", "mcp_name": "Zoho CRM", "updated_at": "2025-08-20T12:09:33.777847", "category": "sales", "name": "create_record", "description": "Create a new record in Zoho CRM module.\n\nRequired fields by module:\n• Leads: Last_Name, Company\n• Contacts: Last_Name  \n• Accounts: Account_Name\n• Deals: Deal_Name, Stage, Closing_Date\n\nCommon optional fields:\n• Email, Phone, Mobile, Website, Description\n• Lead_Source (for Leads), Industry, Annual_Revenue\n\nExample: Create a lead with Last_Name=\"Smith\", Company=\"Acme Corp\", Email=\"<EMAIL>\"\n"}, {"type": "mcp", "id": "3d94e0d9-73c4-4260-b11d-e600c9a1f35f", "mcp_name": "Zoho CRM", "updated_at": "2025-08-20T12:09:33.777847", "category": "sales", "name": "health_check", "description": "Check server health and configuration status.\n\nReturns:\n• Server status and uptime\n• Zoho API connectivity information\n• Configuration details\n• Authentication status\n\nUse this tool to verify the server is running correctly and can connect to Zoho APIs.\n"}, {"type": "mcp", "id": "3d94e0d9-73c4-4260-b11d-e600c9a1f35f", "mcp_name": "Zoho CRM", "updated_at": "2025-08-20T12:09:33.777847", "category": "sales", "name": "get_tool_info", "description": "Get detailed information about available tools and their parameters.\n\nFeatures:\n• Complete parameter specifications for each tool\n• Required vs optional field information  \n• Module-specific field requirements\n• Usage examples and best practices\n• Search operator documentation\n\nUse without tool_name to get info for all tools, or specify tool_name for specific tool details.\n"}, {"type": "mcp", "id": "3d94e0d9-73c4-4260-b11d-e600c9a1f35f", "mcp_name": "Zoho CRM", "updated_at": "2025-08-20T12:09:33.777847", "category": "sales", "name": "search_records", "description": "Search records using advanced criteria with multiple operators.\n\nSearch operators:\n• equals: Exact match\n• contains: Partial match (case-insensitive)  \n• starts_with: Begins with value\n• ends_with: Ends with value\n• less_than, greater_than: Numeric/date comparison\n\nCriteria format:\n• Single: (Field_Name:operator:value)\n• Multiple: ((Field1:equals:value1)and(Field2:contains:value2))\n• Date: (Created_Time:greater_than:2024-01-01T00:00:00Z)\n\nExamples:\n• Find leads by company: (Company:contains:Tech)\n• Multiple criteria: ((Last_Name:equals:<PERSON>)and(Lead_Source:equals:Website))\n• Recent records: (Created_Time:greater_than:2024-01-01T00:00:00Z)\n"}, {"type": "mcp", "id": "3d94e0d9-73c4-4260-b11d-e600c9a1f35f", "mcp_name": "Zoho CRM", "updated_at": "2025-08-20T12:09:33.777847", "category": "sales", "name": "get_records", "description": "Retrieve records from Zoho CRM with pagination and field selection.\n\nFeatures:\n• Pagination support (page, per_page)\n• Field selection to optimize performance\n• Automatic handling of large datasets\n\nCommon fields by module:\n• Leads: Last_Name, Company, Email, Phone, Lead_Source, Created_Time\n• Contacts: Last_Name, First_Name, Email, Phone, Account_Name\n• Accounts: Account_Name, Website, Industry, Annual_Revenue\n• Deals: Deal_Name, Amount, Stage, Closing_Date, Account_Name\n\nExample: Get first 50 leads with specific fields\n"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "insert_row", "description": "Insert a single row at the specified index, shifting existing rows down"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "create_column", "description": "Create a new column in a spreadsheet"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "get_cell", "description": "Fetch the contents of a specific cell in a spreadsheet"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "find_empty_row", "description": "Find the first empty row in a worksheet starting from a specific row"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "insert_multiple_rows", "description": "Insert multiple rows at the specified index, shifting existing rows down"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "find_row", "description": "Find one or more rows by a column and value"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "count_column_values", "description": "Count the total number of values in a specific column"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "set_formula", "description": "Set a formula in a specific cell of a Google Sheet"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "update_cell", "description": "Update a cell in a spreadsheet"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "add_multiple_rows", "description": "Add multiple rows of data to a Google Sheet"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "add_single_row", "description": "Add a single row of data to Google Sheets"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "clear_cell", "description": "Delete the content of a specific cell in a spreadsheet"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "clear_rows", "description": "Delete the content of a row or rows in a spreadsheet. Deleted rows will appear as blank rows"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "create_spreadsheet", "description": "Create a blank spreadsheet or duplicate an existing spreadsheet"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "insert_anchored_note", "description": "Insert a note on a spreadsheet cell"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "delete_rows", "description": "Deletes the specified rows from a spreadsheet"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "list_worksheets", "description": "Get a list of all worksheets in a spreadsheet"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "update_multiple_rows", "description": "Update multiple rows in a spreadsheet defined by a range"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "delete_worksheet", "description": "Delete a specific worksheet"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "get_spreadsheet_by_id", "description": "Returns the spreadsheet at the given ID"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "get_values_in_range", "description": "Get all values or values from a range of cells using A1 notation"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "create_worksheet", "description": "Create a blank worksheet with a title"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "upsert_row", "description": "Upsert a row of data in a Google Sheet"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "copy_worksheet", "description": "Copy an existing worksheet to another Google Sheets file"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "update_row", "description": "Update a row in a spreadsheet"}, {"type": "mcp", "id": "345b1a31-d976-4405-b744-b0752bcc2e4c", "mcp_name": "Google Sheets", "updated_at": "2025-08-26T08:18:29.456373", "category": "general", "name": "insert_comment", "description": "Insert a comment into a spreadsheet"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "dbsize", "description": "Get the number of keys stored in the Redis database\n    "}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "info", "description": "Get Redis server information and statistics.\n\nArgs:\n    section: The section of the info command (default, memory, cpu, etc.).\n\nReturns:\n    A dictionary of server information or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "client_list", "description": "Get a list of connected clients to the Redis server."}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "delete", "description": "Delete a Redis key.\n\nArgs:\n    key (str): The key to delete.\n\nReturns:\n    str: Confirmation message or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "type", "description": "Returns the string representation of the type of the value stored at key\n\nArgs:\n    key (str): The key to check.\n\nReturns:\n    str: The type of key, or none when key doesn't exist\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "expire", "description": "Set an expiration time for a Redis key.\n\nArgs:\n    name: The Redis key.\n    expire_seconds: Time in seconds after which the key should expire.\n\nReturns:\n    A success message or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "rename", "description": "\nRenames a Redis key from old_key to new_key.\n\nArgs:\n    old_key (str): The current name of the Redis key to rename.\n    new_key (str): The new name to assign to the key.\n\nReturns:\n    Dict[str, Any]: A dictionary containing the result of the operation.\n        On success: {\"status\": \"success\", \"message\": \"...\"}\n        On error: {\"error\": \"...\"}\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "scan_keys", "description": "\nScan keys in the Redis database using the SCAN command (non-blocking, production-safe).\n\n⚠️  IMPORTANT: This returns PARTIAL results from one iteration. Use scan_all_keys() \nto get ALL matching keys, or call this function multiple times with the returned cursor\nuntil cursor becomes 0.\n\nThe SCAN command iterates through the keyspace in small chunks, making it safe to use\non large databases without blocking other operations.\n\nArgs:\n    pattern: Pattern to match keys against (default is \"*\" for all keys).\n            Common patterns: \"user:*\", \"cache:*\", \"*:123\", etc.\n    count: Hint for the number of keys to return per iteration (default 100).\n           Redis may return more or fewer keys than this hint.\n    cursor: The cursor position to start scanning from (0 to start from beginning).\n            To continue scanning, use the cursor value returned from previous call.\n\nReturns:\n    A dictionary containing:\n    - 'cursor': Next cursor position (0 means scan is complete)\n    - 'keys': List of keys found in this iteration (PARTIAL RESULTS)\n    - 'total_scanned': Number of keys returned in this batch\n    - 'scan_complete': Boolean indicating if scan is finished\n    Or an error message if something goes wrong.\n    \nExample usage:\n    First call: scan_keys(\"user:*\") -> returns cursor=1234, keys=[...], scan_complete=False\n    Next call: scan_keys(\"user:*\", cursor=1234) -> continues from where it left off\n    Final call: returns cursor=0, scan_complete=True when done\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "scan_all_keys", "description": "\nScan and return ALL keys matching a pattern using multiple SCAN iterations.\n\nThis function automatically handles the SCAN cursor iteration to collect all matching keys.\nIt's safer than KEYS * for large databases but will still collect all results in memory.\n\n⚠️  WARNING: With very large datasets (millions of keys), this may consume significant memory.\nFor large-scale operations, consider using scan_keys() with manual iteration instead.\n\nArgs:\n    pattern: Pattern to match keys against (default is \"*\" for all keys).\n    batch_size: Number of keys to scan per iteration (default 100).\n\nReturns:\n    A list of all keys matching the pattern or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "get_indexes", "description": "List of indexes in the Redis database\n\nReturns:\n    str: A JSON string containing the list of indexes or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "get_index_info", "description": "Retrieve schema and information about a specific Redis index using FT.INFO.\n\nArgs:\n    index_name (str): The name of the index to retrieve information about.\n\nReturns:\n    str: Information about the specified index or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "get_indexed_keys_number", "description": "Retrieve the number of indexed keys by the index\n\nArgs:\n    index_name (str): The name of the index to retrieve information about.\n\nReturns:\n    int: Number of indexed keys\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "create_vector_index_hash", "description": "\nCreate a Redis 8 vector similarity index using HNSW on a Redis hash.\n\nThis function sets up a Redis index for approximate nearest neighbor (ANN)\nsearch using the HNSW algorithm and float32 vector embeddings.\n\nArgs:\n    index_name: The name of the Redis index to create. Unless specifically required, use the default name for the index.\n    prefix: The key prefix used to identify documents to index (e.g., 'doc:'). Unless specifically required, use the default prefix.\n    vector_field: The name of the vector field to be indexed for similarity search. Unless specifically required, use the default field name\n    dim: The dimensionality of the vectors stored under the vector_field.\n    distance_metric: The distance function to use (e.g., 'COSINE', 'L2', 'IP').\n\nReturns:\n    A string indicating whether the index was created successfully or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "vector_search_hash", "description": "\nPerform a KNN vector similarity search using Redis 8 or later version on vectors stored in hash data structures.\n\nArgs:\n    query_vector: List of floats to use as the query vector.\n    index_name: Name of the Redis index. Unless specifically specified, use the default index name.\n    vector_field: Name of the indexed vector field. Unless specifically required, use the default field name\n    k: Number of nearest neighbors to return.\n    return_fields: List of fields to return (optional).\n\nReturns:\n    A list of matched documents or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "hset", "description": "Set a field in a hash stored at key with an optional expiration time.\n\nArgs:\n    name: The Redis hash key.\n    key: The field name inside the hash.\n    value: The value to set.\n    expire_seconds: Optional; time in seconds after which the key should expire.\n\nReturns:\n    A success message or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "hget", "description": "Get the value of a field in a Redis hash.\n\nArgs:\n    name: The Redis hash key.\n    key: The field name inside the hash.\n\nReturns:\n    The field value or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "hdel", "description": "Delete a field from a Redis hash.\n\nArgs:\n    name: The Redis hash key.\n    key: The field name inside the hash.\n\nReturns:\n    A success message or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "hgetall", "description": "Get all fields and values from a Redis hash.\n\nArgs:\n    name: The Redis hash key.\n\nReturns:\n    A dictionary of field-value pairs or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "hexists", "description": "Check if a field exists in a Redis hash.\n\nArgs:\n    name: The Redis hash key.\n    key: The field name inside the hash.\n\nReturns:\n    True if the field exists, False otherwise.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "set_vector_in_hash", "description": "Store a vector as a field in a Redis hash.\n\nArgs:\n    name: The Redis hash key.\n    vector_field: The field name inside the hash. Unless specifically required, use the default field name\n    vector: The vector (list of numbers) to store in the hash.\n\nReturns:\n    True if the vector was successfully stored, False otherwise.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "get_vector_from_hash", "description": "Retrieve a vector from a Redis hash and convert it back from binary blob.\n\nArgs:\n    name: The Redis hash key.\n    vector_field: The field name inside the hash. Unless specifically required, use the default field name\n\nReturns:\n    The vector as a list of floats, or an error message if retrieval fails.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "lpush", "description": "Push a value onto the left of a Redis list and optionally set an expiration time."}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "rpush", "description": "Push a value onto the right of a Redis list and optionally set an expiration time."}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "lpop", "description": "Remove and return the first element from a Redis list."}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "rpop", "description": "Remove and return the last element from a Redis list."}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "lrange", "description": "Get elements from a Redis list within a specific range.\n\nReturns:\nstr: A JSON string containing the list of elements or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "llen", "description": "Get the length of a Redis list."}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "set", "description": "Set a Redis string value with an optional expiration time.\n\nArgs:\n    key (str): The key to set.\n    value (str): The value to store.\n    expiration (int, optional): Expiration time in seconds.\n\nReturns:\n    str: Confirmation message or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "get", "description": "Get a Redis string value.\n\nArgs:\n    key (str): The key to retrieve.\n\nReturns:\n    str: The stored value or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "json_set", "description": "Set a JSON value in Redis at a given path with an optional expiration time.\n\nArgs:\n    name: The Redis key where the JSON document is stored.\n    path: The JSON path where the value should be set.\n    value: The JSON value to store.\n    expire_seconds: Optional; time in seconds after which the key should expire.\n\nReturns:\n    A success message or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "json_get", "description": "Retrieve a JSON value from Redis at a given path.\n\nArgs:\n    name: The Redis key where the JSON document is stored.\n    path: The JSON path to retrieve (default: root '$').\n\nReturns:\n    The retrieved JSON value or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "json_del", "description": "Delete a JSON value from Redis at a given path.\n\nArgs:\n    name: The Redis key where the JSON document is stored.\n    path: The JSON path to delete (default: root '$').\n\nReturns:\n    A success message or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "zadd", "description": "Add a member to a Redis sorted set with an optional expiration time.\n\nArgs:\n    key (str): The sorted set key.\n    score (float): The score of the member.\n    member (str): The member to add.\n    expiration (int, optional): Expiration time in seconds.\n\nReturns:\n    str: Confirmation message or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "zrange", "description": "Retrieve a range of members from a Redis sorted set.\n\nArgs:\n    key (str): The sorted set key.\n    start (int): The starting index.\n    end (int): The ending index.\n    with_scores (bool, optional): Whether to include scores in the result.\n\nReturns:\n    str: The sorted set members in the given range or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "zrem", "description": "Remove a member from a Redis sorted set.\n\nArgs:\n    key (str): The sorted set key.\n    member (str): The member to remove.\n\nReturns:\n    str: Confirmation message or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "sadd", "description": "Add a value to a Redis set with an optional expiration time.\n\nArgs:\n    name: The Redis set key.\n    value: The value to add to the set.\n    expire_seconds: Optional; time in seconds after which the set should expire.\n\nReturns:\n    A success message or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "srem", "description": "Remove a value from a Redis set.\n\nArgs:\n    name: The Redis set key.\n    value: The value to remove from the set.\n\nReturns:\n    A success message or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "smembers", "description": "Get all members of a Redis set.\n\nArgs:\n    name: The Redis set key.\n\nReturns:\n    A list of values in the set or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "xadd", "description": "Add an entry to a Redis stream with an optional expiration time.\n\nArgs:\n    key (str): The stream key.\n    fields (dict): The fields and values for the stream entry.\n    expiration (int, optional): Expiration time in seconds.\n\nReturns:\n    str: The ID of the added entry or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "xrange", "description": "Read entries from a Redis stream.\n\nArgs:\n    key (str): The stream key.\n    count (int, optional): Number of entries to retrieve.\n\nReturns:\n    str: The retrieved stream entries or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "xdel", "description": "Delete an entry from a Redis stream.\n\nArgs:\n    key (str): The stream key.\n    entry_id (str): The ID of the entry to delete.\n\nReturns:\n    str: Confirmation message or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "publish", "description": "Publish a message to a Redis channel.\n\nArgs:\n    channel: The Redis channel to publish to.\n    message: The message to send.\n\nReturns:\n    A success message or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "subscribe", "description": "Subscribe to a Redis channel.\n\nArgs:\n    channel: The Redis channel to subscribe to.\n\nReturns:\n    A success message or an error message.\n"}, {"type": "mcp", "id": "a9b79033-4ddb-4d17-9447-7a891855caf9", "mcp_name": "Redis", "updated_at": "2025-08-05T10:30:43.461911", "category": "general", "name": "unsubscribe", "description": "Unsubscribe from a Redis channel.\n\nArgs:\n    channel: The Redis channel to unsubscribe from.\n\nReturns:\n    A success message or an error message.\n"}, {"type": "mcp", "id": "a1700776-e64f-4270-9e4e-3f7a85383919", "mcp_name": "script generation", "updated_at": "2025-07-14T11:42:37.361221", "category": "marketing", "name": "research", "description": "Research for the given topic"}, {"type": "mcp", "id": "a1700776-e64f-4270-9e4e-3f7a85383919", "mcp_name": "script generation", "updated_at": "2025-07-14T11:42:37.361221", "category": "marketing", "name": "script_generate", "description": "Provide topic and keyword to generator <PERSON><PERSON><PERSON>"}, {"type": "mcp", "id": "af3db8c7-a9c8-428c-8f21-db54da1c0d82", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-25T12:27:06.059662", "category": "general", "name": "send_bulk_mail", "description": "Send bulk mail to multiple recipients using SendGrid"}, {"type": "mcp", "id": "af3db8c7-a9c8-428c-8f21-db54da1c0d82", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-25T12:27:06.059662", "category": "general", "name": "send_mail", "description": "Send mail to the user using SendGrid"}, {"type": "mcp", "id": "d129be85-8c05-4f45-b742-f33101082ee9", "mcp_name": "Strapi MCP", "updated_at": "2025-09-01T06:51:00.765397", "category": "general", "name": "strapi_get_components", "description": "Get component schemas from Strapi"}, {"type": "mcp", "id": "d129be85-8c05-4f45-b742-f33101082ee9", "mcp_name": "Strapi MCP", "updated_at": "2025-09-01T06:51:00.765397", "category": "general", "name": "strapi_list_servers", "description": "List all configured Strapi servers"}, {"type": "mcp", "id": "d129be85-8c05-4f45-b742-f33101082ee9", "mcp_name": "Strapi MCP", "updated_at": "2025-09-01T06:51:00.765397", "category": "general", "name": "strapi_upload_media", "description": "Upload media files to Strapi"}, {"type": "mcp", "id": "d129be85-8c05-4f45-b742-f33101082ee9", "mcp_name": "Strapi MCP", "updated_at": "2025-09-01T06:51:00.765397", "category": "general", "name": "strapi_rest", "description": "Execute REST API operations on Strapi"}, {"type": "mcp", "id": "d129be85-8c05-4f45-b742-f33101082ee9", "mcp_name": "Strapi MCP", "updated_at": "2025-09-01T06:51:00.765397", "category": "general", "name": "strapi_get_content_types", "description": "Get content type schemas from a Strapi server"}, {"type": "mcp", "id": "e0ad8dfe-db0a-4a5c-ac80-4aa5a64dbcb4", "mcp_name": "Bible MCP", "updated_at": "2025-08-15T08:18:33.785572", "category": "general", "name": "get_random_bible_verse", "description": "\n    Get a random Bible verse.\n\n    Args:\n        book_ids: Optional comma-separated list of book IDs (e.g., \"GEN,JHN\")\n                 or special strings \"OT\" (Old Testament) or \"NT\" (New Testament)\n\n    Returns:\n        Random verse data including reference, text, and translation info\n    "}, {"type": "mcp", "id": "e0ad8dfe-db0a-4a5c-ac80-4aa5a64dbcb4", "mcp_name": "Bible MCP", "updated_at": "2025-08-15T08:18:33.785572", "category": "general", "name": "get_bible_verse", "description": "\n    Get a specific Bible verse or passage.\n\n    Args:\n        reference: Bible reference (e.g., \"John 3:16\", \"Genesis 1:1-3\")\n        translation: Translation identifier (default: \"web\" for World English Bible)\n\n    Returns:\n        Verse data including reference, text, and translation info\n    "}, {"type": "mcp", "id": "e0ad8dfe-db0a-4a5c-ac80-4aa5a64dbcb4", "mcp_name": "Bible MCP", "updated_at": "2025-08-15T08:18:33.785572", "category": "general", "name": "list_bible_translations", "description": "\n    Get list of all available Bible translations.\n\n    Returns:\n        List of available translations with their identifiers and names\n    "}, {"type": "mcp", "id": "e0ad8dfe-db0a-4a5c-ac80-4aa5a64dbcb4", "mcp_name": "Bible MCP", "updated_at": "2025-08-15T08:18:33.785572", "category": "general", "name": "list_bible_books", "description": "\n    Get list of books for a specific Bible translation.\n\n    Args:\n        translation: Translation identifier (default: \"web\")\n\n    Returns:\n        List of books with their identifiers and names\n    "}, {"type": "mcp", "id": "e0ad8dfe-db0a-4a5c-ac80-4aa5a64dbcb4", "mcp_name": "Bible MCP", "updated_at": "2025-08-15T08:18:33.785572", "category": "general", "name": "list_bible_chapters", "description": "\n    Get chapters for a specific Bible book.\n\n    Args:\n        book: Book identifier (e.g., \"JHN\" for <PERSON>, \"GEN\" for Genesis)\n        translation: Translation identifier (default: \"web\")\n\n    Returns:\n        List of chapters for the specified book\n    "}, {"type": "mcp", "id": "06df097c-d38f-443b-a57b-886a9b14b1b2", "mcp_name": "mem0-mcp", "updated_at": "2025-08-18T06:48:19.711919", "category": "engineering", "name": "add-memory", "description": "Add a new memory. This method is called everytime the user informs anything about themselves, their preferences, or anything that has any relevent information whcih can be useful in the future conversation. This can also be called when the user asks you to remember something."}, {"type": "mcp", "id": "06df097c-d38f-443b-a57b-886a9b14b1b2", "mcp_name": "mem0-mcp", "updated_at": "2025-08-18T06:48:19.711919", "category": "engineering", "name": "search-memories", "description": "Search through stored memories. This method is called ANYTIME the user asks anything."}, {"type": "mcp", "id": "f506eb7c-a648-4873-8055-e2273be08478", "mcp_name": "Code-Runner-Mcp", "updated_at": "2025-08-22T10:06:33.574903", "category": "engineering", "name": "execute_code", "description": "Execute JavaScript or Python code securely with comprehensive error handling and security measures"}, {"type": "mcp", "id": "f506eb7c-a648-4873-8055-e2273be08478", "mcp_name": "Code-Runner-Mcp", "updated_at": "2025-08-22T10:06:33.574903", "category": "engineering", "name": "get_capabilities", "description": "Get information about supported languages and execution capabilities"}, {"type": "mcp", "id": "f506eb7c-a648-4873-8055-e2273be08478", "mcp_name": "Code-Runner-Mcp", "updated_at": "2025-08-22T10:06:33.574903", "category": "engineering", "name": "validate_code", "description": "Validate code for security and syntax issues without executing it"}, {"type": "mcp", "id": "f506eb7c-a648-4873-8055-e2273be08478", "mcp_name": "Code-Runner-Mcp", "updated_at": "2025-08-22T10:06:33.574903", "category": "engineering", "name": "execute_code_with_variables", "description": "Execute JavaScript or Python code with dynamic input variables that can be defined and passed as key-value pairs"}, {"type": "mcp", "id": "08748776-aad0-4dcf-b2dd-935e934550df", "mcp_name": "Postgres", "updated_at": "2025-08-05T10:31:04.016165", "category": "general", "name": "query", "description": "Execute a SQL query against the PostgreSQL database."}, {"type": "mcp", "id": "08748776-aad0-4dcf-b2dd-935e934550df", "mcp_name": "Postgres", "updated_at": "2025-08-05T10:31:04.016165", "category": "general", "name": "list_schemas", "description": "List all schemas in the database."}, {"type": "mcp", "id": "08748776-aad0-4dcf-b2dd-935e934550df", "mcp_name": "Postgres", "updated_at": "2025-08-05T10:31:04.016165", "category": "general", "name": "list_tables", "description": "List all tables in a specific schema.\n    \n    Args:\n        db_schema: The schema name to list tables from (defaults to 'public')\n    "}, {"type": "mcp", "id": "08748776-aad0-4dcf-b2dd-935e934550df", "mcp_name": "Postgres", "updated_at": "2025-08-05T10:31:04.016165", "category": "general", "name": "describe_table", "description": "Get detailed information about a table.\n    \n    Args:\n        table_name: The name of the table to describe\n        db_schema: The schema name (defaults to 'public')\n    "}, {"type": "mcp", "id": "08748776-aad0-4dcf-b2dd-935e934550df", "mcp_name": "Postgres", "updated_at": "2025-08-05T10:31:04.016165", "category": "general", "name": "get_foreign_keys", "description": "Get foreign key information for a table.\n    \n    Args:\n        table_name: The name of the table to get foreign keys from\n        db_schema: The schema name (defaults to 'public')\n    "}, {"type": "mcp", "id": "08748776-aad0-4dcf-b2dd-935e934550df", "mcp_name": "Postgres", "updated_at": "2025-08-05T10:31:04.016165", "category": "general", "name": "find_relationships", "description": "Find both explicit and implied relationships for a table.\n    \n    Args:\n        table_name: The name of the table to analyze relationships for\n        db_schema: The schema name (defaults to 'public')\n    "}, {"type": "mcp", "id": "d10c53b8-4224-4145-8f34-01633fe3e3ce", "mcp_name": "Google Forms", "updated_at": "2025-08-05T10:38:03.586828", "category": "general", "name": "get_google_form", "description": "Get a Google Form by ID"}, {"type": "mcp", "id": "d10c53b8-4224-4145-8f34-01633fe3e3ce", "mcp_name": "Google Forms", "updated_at": "2025-08-05T10:38:03.586828", "category": "general", "name": "create_google_form", "description": "Create a new Google Form"}, {"type": "mcp", "id": "d10c53b8-4224-4145-8f34-01633fe3e3ce", "mcp_name": "Google Forms", "updated_at": "2025-08-05T10:38:03.586828", "category": "general", "name": "validate_google_credentials", "description": "Validate Google OAuth credentials and check API access"}, {"type": "mcp", "id": "d10c53b8-4224-4145-8f34-01633fe3e3ce", "mcp_name": "Google Forms", "updated_at": "2025-08-05T10:38:03.586828", "category": "general", "name": "get_google_form_responses", "description": "Get responses for a Google Form"}, {"type": "mcp", "id": "d10c53b8-4224-4145-8f34-01633fe3e3ce", "mcp_name": "Google Forms", "updated_at": "2025-08-05T10:38:03.586828", "category": "general", "name": "update_google_form", "description": "Update a Google Form with new questions"}, {"type": "mcp", "id": "7acabafe-c215-4813-9993-768393c978ea", "mcp_name": "Docker Sandbox", "updated_at": "2025-08-15T08:18:09.225601", "category": "engineering", "name": "setup_container", "description": "Set up a Docker container with the specified image name and tag."}, {"type": "mcp", "id": "7acabafe-c215-4813-9993-768393c978ea", "mcp_name": "Docker Sandbox", "updated_at": "2025-08-15T08:18:09.225601", "category": "engineering", "name": "get_preview_url", "description": "Get preview url of provided container id"}, {"type": "mcp", "id": "7acabafe-c215-4813-9993-768393c978ea", "mcp_name": "Docker Sandbox", "updated_at": "2025-08-15T08:18:09.225601", "category": "engineering", "name": "view_file_paths_in_container_dir", "description": "View all file paths in a Docker container's workspace."}, {"type": "mcp", "id": "7acabafe-c215-4813-9993-768393c978ea", "mcp_name": "Docker Sandbox", "updated_at": "2025-08-15T08:18:09.225601", "category": "engineering", "name": "add_dependency", "description": "Use this tool to add a dependency to the project. The dependency should be a valid npm package name."}, {"type": "mcp", "id": "7acabafe-c215-4813-9993-768393c978ea", "mcp_name": "Docker Sandbox", "updated_at": "2025-08-15T08:18:09.225601", "category": "engineering", "name": "write", "description": "Use this tool to write to a file. Overwrites the existing file if there is one. The file path should be relative to the project root."}, {"type": "mcp", "id": "7acabafe-c215-4813-9993-768393c978ea", "mcp_name": "Docker Sandbox", "updated_at": "2025-08-15T08:18:09.225601", "category": "engineering", "name": "view", "description": "Use this tool to read the contents of a file. The file path should be relative to the project root. You can optionally specify line ranges to read using the lines parameter."}, {"type": "mcp", "id": "7acabafe-c215-4813-9993-768393c978ea", "mcp_name": "Docker Sandbox", "updated_at": "2025-08-15T08:18:09.225601", "category": "engineering", "name": "line_replace", "description": "Line-Based Search and Replace Tool. Use this tool to find and replace specific content in a file using explicit line numbers."}, {"type": "mcp", "id": "7acabafe-c215-4813-9993-768393c978ea", "mcp_name": "Docker Sandbox", "updated_at": "2025-08-15T08:18:09.225601", "category": "engineering", "name": "download_to_repo", "description": "Download a file from a URL and save it to the repository. Useful for downloading images, assets, or other files from URLs."}, {"type": "mcp", "id": "7acabafe-c215-4813-9993-768393c978ea", "mcp_name": "Docker Sandbox", "updated_at": "2025-08-15T08:18:09.225601", "category": "engineering", "name": "remove_dependency", "description": "Use this tool to uninstall a package from the project."}, {"type": "mcp", "id": "7acabafe-c215-4813-9993-768393c978ea", "mcp_name": "Docker Sandbox", "updated_at": "2025-08-15T08:18:09.225601", "category": "engineering", "name": "rename", "description": "You MUST use this tool to rename a file instead of creating new files and deleting old ones."}, {"type": "mcp", "id": "7acabafe-c215-4813-9993-768393c978ea", "mcp_name": "Docker Sandbox", "updated_at": "2025-08-15T08:18:09.225601", "category": "engineering", "name": "delete", "description": "Use this tool to delete a file. The file path should be relative to the project root."}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "update_meta_campaign", "description": "Update an existing Meta Ads campaign with new configuration"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "update_campaign_status", "description": "Update the status of a Meta Ads campaign (ACTIVE, PAUSED, etc.)"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_campaign_details", "description": "Get detailed information about a Meta Ads campaign"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_ad_accounts", "description": "Get ad accounts accessible by a user"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_account_info", "description": "Get detailed information about a specific ad account"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_account_pages", "description": "Get pages associated with a Meta Ads account"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_campaigns", "description": "Get campaigns for a Meta Ads account with optional filtering"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_campaign_details", "description": "Get detailed information about a specific campaign"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_adsets", "description": "Get ad sets for a Meta Ads account with optional filtering by campaign"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_adset_details", "description": "Get detailed information about a specific ad set"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "create_adset", "description": "Create a new ad set in a Meta Ads account"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "update_adset", "description": "Update an ad set with new settings including frequency caps"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_ads", "description": "Get ads for a Meta Ads account with optional filtering"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_ad_details", "description": "Get detailed information about a specific ad"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "update_ad", "description": "Update an ad with new settings"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_ad_creatives", "description": "Get creative details for a specific ad"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "upload_ad_image", "description": "Upload an image to use in Meta Ads creatives"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_ad_image", "description": "Get, download, and visualize a Meta ad image in one step"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_insights", "description": "Get performance insights for a campaign, ad set, ad or account"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "create_budget_schedule", "description": "Create a budget schedule for a Meta Ads campaign"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_custom_audiences", "description": "Get custom audiences including email lists, purchase data, and website visitor audiences"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_lookalike_audiences", "description": "Get lookalike audiences that are similar to your existing customers"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_audience_insights", "description": "Get detailed insights and analytics for a specific audience including size estimates and demographics"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_campaign_insights", "description": "Get historical campaign performance data including customer engagement metrics and conversion data"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_ad_account_insights", "description": "Get comprehensive ad account insights including customer behavior and demographic breakdowns"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_conversion_data", "description": "Get detailed conversion tracking data to understand customer actions and purchase behavior"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_pixel_events", "description": "Get Facebook Pixel events data to track customer interactions and website behavior"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_customer_segments", "description": "Get customer segments and audience categorization data for targeted marketing"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "get_audience_demographics", "description": "Get detailed demographic information about your audiences including age, gender, location, and interests"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "gather_ad_requirements", "description": "Comprehensive tool to gather all requirements for creating Meta Ads campaigns, ad sets, and creatives"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "create_ad", "description": "Create a new ad with an existing creative"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "create_meta_campaign", "description": "Create a Meta Ads campaign with specified configuration"}, {"type": "mcp", "id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "mcp_name": "MetaAds", "updated_at": "2025-08-15T08:29:31.789097", "category": "marketing", "name": "create_ad_creative", "description": "Create a new ad creative using an uploaded image hash"}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_commit", "description": "Create a commit with the specified message."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_directory_structure", "description": "Clone a Git repository and return its directory structure in a tree format."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_read_files", "description": "Read the contents of specified files in a given git repository."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_branch_diff", "description": "Compare two branches and show files changed between them."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_checkout_branch", "description": "Create and/or checkout a branch."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_merge_branch", "description": "Merge a source branch into the current or target branch."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_commit_history", "description": "Get commit history for a branch with optional filtering."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_commits_details", "description": "Get detailed information about commits including full messages and diffs."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_track", "description": "Track (stage) specific files or all files."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_local_changes", "description": "Get uncommitted changes in the working directory."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_search_code", "description": "Search for patterns in repository code."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_push", "description": "Push changes to a remote repository."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_pull", "description": "Pull changes from a remote repository."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_stash", "description": "Create or apply a stash."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_create_tag", "description": "Create a tag."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_rebase", "description": "Rebase the current branch onto another branch or commit."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_config", "description": "Configure git settings for the repository."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_reset", "description": "Reset repository to specified commit or state."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_archive", "description": "Create a git archive (zip or tar)."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_attributes", "description": "Manage git attributes for files."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_blame", "description": "Get blame information for a file."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_clean", "description": "Perform git clean operations."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_lfs", "description": "Manage Git LFS (Large File Storage)."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_lfs_fetch", "description": "Fetch LFS objects from the remote repository."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_revert", "description": "Revert the current branch to a commit or state."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_delete_branch", "description": "Delete a branch from the repository."}, {"type": "mcp", "id": "445f4def-6abd-4c49-b2ba-6f588fa30dc1", "mcp_name": "Git Commands", "updated_at": "2025-08-26T12:45:41.264149", "category": "general", "name": "git_hooks", "description": "Manage git hooks in the repository."}, {"type": "mcp", "id": "2e2c611f-9338-4193-a416-e332e1a32267", "mcp_name": "Apollo IO", "updated_at": "2025-08-25T09:48:28.418616", "category": "sales", "name": "bulk_people_enrichment", "description": "Use the Bulk People Enrichment endpoint to enrich data for up to 10 people in a single request. Supports revealing personal emails and phone numbers."}, {"type": "mcp", "id": "2e2c611f-9338-4193-a416-e332e1a32267", "mcp_name": "Apollo IO", "updated_at": "2025-08-25T09:48:28.418616", "category": "sales", "name": "organization_enrichment", "description": "Use the Organization Enrichment endpoint to enrich data for 1 company"}, {"type": "mcp", "id": "2e2c611f-9338-4193-a416-e332e1a32267", "mcp_name": "Apollo IO", "updated_at": "2025-08-25T09:48:28.418616", "category": "sales", "name": "people_search", "description": "Use the People Search endpoint to find people"}, {"type": "mcp", "id": "2e2c611f-9338-4193-a416-e332e1a32267", "mcp_name": "Apollo IO", "updated_at": "2025-08-25T09:48:28.418616", "category": "sales", "name": "organization_search", "description": "Use the Organization Search endpoint to find organizations"}, {"type": "mcp", "id": "2e2c611f-9338-4193-a416-e332e1a32267", "mcp_name": "Apollo IO", "updated_at": "2025-08-25T09:48:28.418616", "category": "sales", "name": "organization_job_postings", "description": "Use the Organization Job Postings endpoint to find job postings for a specific organization"}, {"type": "mcp", "id": "2e2c611f-9338-4193-a416-e332e1a32267", "mcp_name": "Apollo IO", "updated_at": "2025-08-25T09:48:28.418616", "category": "sales", "name": "get_person_email", "description": "Get email address for a person using their Apollo ID"}, {"type": "mcp", "id": "2e2c611f-9338-4193-a416-e332e1a32267", "mcp_name": "Apollo IO", "updated_at": "2025-08-25T09:48:28.418616", "category": "sales", "name": "employees_of_company", "description": "Find employees of a company using company name or website/LinkedIn URL"}, {"type": "mcp", "id": "2e2c611f-9338-4193-a416-e332e1a32267", "mcp_name": "Apollo IO", "updated_at": "2025-08-25T09:48:28.418616", "category": "sales", "name": "people_enrichment", "description": "Use the People Enrichment endpoint to enrich data for 1 person. Supports revealing personal emails and phone numbers."}, {"type": "mcp", "id": "9e0144c3-39c8-46bd-a7f7-47925756e78a", "mcp_name": "content-extractor-mcp", "updated_at": "2025-08-04T05:20:07.691968", "category": "general", "name": "scrape_web", "description": "Extract content from a web page or article link."}, {"type": "mcp", "id": "9e0144c3-39c8-46bd-a7f7-47925756e78a", "mcp_name": "content-extractor-mcp", "updated_at": "2025-08-04T05:20:07.691968", "category": "general", "name": "scrape_web_using_browser_use", "description": "Use a browser-based approach to scrape data from a web page."}, {"type": "mcp", "id": "9e0144c3-39c8-46bd-a7f7-47925756e78a", "mcp_name": "content-extractor-mcp", "updated_at": "2025-08-04T05:20:07.691968", "category": "general", "name": "scrape_web_using_fire_crawler", "description": "Utilize the FireCrawl tool to scrape data from a web page."}, {"type": "mcp", "id": "9e0144c3-39c8-46bd-a7f7-47925756e78a", "mcp_name": "content-extractor-mcp", "updated_at": "2025-08-04T05:20:07.691968", "category": "general", "name": "generate_subtitle", "description": "Generate subtitles for a video by processing its audio."}, {"type": "mcp", "id": "796faf92-17eb-4c8f-bd22-185418425862", "mcp_name": "Website Generator", "updated_at": "2025-07-24T16:59:29.371058", "category": "general", "name": "repo_setup", "description": "\n    Setup a new repository for a website project by cloning a template, creating a GitHub repo, and pushing the code.\n\n    This tool performs the following steps:\n    1. <PERSON>lone the React Vite template repository directly to base directory\n    2. Remove the .git folder from the cloned repository\n    3. Create a new repository on GitHub with the given project name\n    4. Push the code to the new GitHub repository\n    5. Optionally deploy to AWS Amplify (if requested)\n\n    Args:\n        project_name: Name of the project and GitHub repository\n        description: Optional description for the GitHub repository\n        deploy_to_amplify: Whether to deploy to AWS Amplify (optional, not implemented yet)\n\n    Returns:\n        Status message with repository URL and deployment information\n    "}, {"type": "mcp", "id": "796faf92-17eb-4c8f-bd22-185418425862", "mcp_name": "Website Generator", "updated_at": "2025-07-24T16:59:29.371058", "category": "general", "name": "create_file", "description": "\n    Create a new file in the project with content generated by the AI Agent.\n\n    This tool performs the following steps:\n    1. Get the file_name, file_path and content from the AI Agent\n    2. Create a new file with the given file_name at the file_path\n    3. Write the content in the file\n\n    Args:\n        file_name: Name of the file to create (e.g., \"index.html\", \"app.js\")\n        file_path: Directory path where the file should be created (e.g., \"./src\", \"./public\")\n        content: Content to write to the file\n\n    Returns:\n        Status message indicating success or failure\n    "}, {"type": "mcp", "id": "796faf92-17eb-4c8f-bd22-185418425862", "mcp_name": "Website Generator", "updated_at": "2025-07-24T16:59:29.371058", "category": "general", "name": "push_changes", "description": "\n    Commit the changes made to the project and push them to the remote repository.\n\n    This tool performs the following steps:\n    1. Get the project name from the AI Agent\n    2. Locate the project directory in the base directory\n    3. Check if there are any changes made to the project\n    4. If there are no changes, it will return\n    5. If there are changes, it will stage the changes\n    6. Commit the changes with an auto-generated commit message\n    7. Push the changes to the remote repository\n\n    Args:\n        project_name: Name of the project (same as used in repo_setup)\n\n    Returns:\n        Status message indicating success or failure\n    "}, {"type": "mcp", "id": "796faf92-17eb-4c8f-bd22-185418425862", "mcp_name": "Website Generator", "updated_at": "2025-07-24T16:59:29.371058", "category": "general", "name": "read_file", "description": "\n    Read the contents of a file.\n\n    This tool allows reading the contents of any file in the project or system.\n    Useful for examining configuration files, source code, or any text-based files.\n\n    Args:\n        file_path: Path to the file to read (can be relative or absolute)\n\n    Returns:\n        File contents or error message\n    "}, {"type": "mcp", "id": "796faf92-17eb-4c8f-bd22-185418425862", "mcp_name": "Website Generator", "updated_at": "2025-07-24T16:59:29.371058", "category": "general", "name": "list_files", "description": "\n    List all files and directories in the specified directory.\n\n    This tool provides a directory listing showing files and subdirectories.\n    Files are marked with [FILE] and directories with [DIR] prefixes.\n\n    Args:\n        directory_path: Path to the directory to list (can be relative or absolute)\n\n    Returns:\n        Directory listing or error message\n    "}, {"type": "mcp", "id": "796faf92-17eb-4c8f-bd22-185418425862", "mcp_name": "Website Generator", "updated_at": "2025-07-24T16:59:29.371058", "category": "general", "name": "update_file", "description": "\n    Update the contents of an existing file.\n\n    This tool overwrites the entire content of an existing file with new content.\n    The file must already exist - use create_file to create new files.\n\n    Args:\n        file_path: Path to the file to update (can be relative or absolute)\n        new_content: New content to write to the file\n\n    Returns:\n        Success message or error message\n    "}, {"type": "mcp", "id": "e65f7356-a71f-4b24-841a-40e0622ed1e6", "mcp_name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-08-15T08:19:38.329957", "category": "general", "name": "send_sms", "description": "Send sms to the user using Twilio"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Listing Products", "description": "List products for a user (optionally filter by campaign_id)"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Creating a New Product", "description": "Create a product for a user and link it to a campaign"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Creating a New Campaign", "description": "Create a new campaign"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Fetching Campaign Details", "description": "Get campaign details by conversation ID or campaign ID (at least one must be provided)"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Fetching Campaigns", "description": "Get list of campaigns for user with optional filtering"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Updating a Campaign", "description": "Update campaign details"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Updating a Campaign Status", "description": "Update campaign status"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Fetching Campaign Stats", "description": "Get campaign statistics and performance metrics"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Fetching Active Campaigns", "description": "Get active campaigns for user"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Fetching Product Details", "description": "Fetch product details by product_id and user_id"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Updating a Product", "description": "Update product by product_id and user_id"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Adding a Customer Meeting", "description": "Add a customer meeting"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Starting Prospect Generation Workflow", "description": "Start prospect generation workflow for a campaign"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Listing Customers", "description": "List customers for a user (optionally filter by campaign_id and communication status)"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Listing Customers with Pagination", "description": "List customers for a user (optionally filter by campaign_id and communication status) with pagination"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Creating Customers", "description": "Bulk create customers for a campaign from a list of Apollo person objects"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Fetching a Customer", "description": "Fetch customer details by customer_id and user_id"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Updating a Customer", "description": "Update customer persona, action_item, relevant_score, and classification fields"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Fetching Customer Emails", "description": "Fetch only customer email addresses for a user (optionally filter by campaign_id and communication status)"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Finding Total number of propsects based on ICP", "description": "Use Apollo API to find total number of prospects matching given ICP filters"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Fetching User Settings", "description": "Fetch settings for a user; if missing, create defaults and return"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Creating User Settings", "description": "Create settings for a user (idempotent; returns existing if present)"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Updating User Settings", "description": "Update settings for a user"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Fetching Available Workflow Types", "description": "Get list of available workflow type identifiers from admin settings"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Configuring User Workflows", "description": "Configure user workflows by cloning all admin workflows for the user"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Creating a New Email Conversation", "description": "Create a new email conversation between customer and user"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Replying an Email Conversation from a Customer", "description": "Create an email conversation record from a customer's reply using from/to emails (resolves user_id, customer_id, latest campaign)"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Fetching an Email Conversation", "description": "Get email conversation by ID"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Fetching Email Conversations", "description": "Get email conversations with optional filtering"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Marking Email Conversations as Read", "description": "Mark all unread email conversations between a user and customer as read"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Fetching Email Conversations with Pa<PERSON>ation", "description": "Fetch email conversations with enhanced pagination, sorting, and is_more key"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Creating a New SMS Conversation", "description": "Create a new SMS conversation between customer and user"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Replying an SMS Conversation from a Customer", "description": "Create an SMS conversation record from a customer's reply using phone numbers (resolves user_id, customer_id, latest campaign)"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Fetching an SMS Conversation", "description": "Get SMS conversation by ID"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Fetching SMS Conversations", "description": "Get SMS conversations with optional filtering"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Marking SMS Conversations as Read", "description": "Mark all unread SMS conversations between a user and customer as read"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Fetching SMS Conversations with Pagination", "description": "Fetch SMS conversations with enhanced pagination, sorting, and is_more key"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Fetching Campaign Aggregated Conversations", "description": "Aggregate email and SMS conversations by customer for a campaign, returning most recent conversation per customer with pagination"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Fetching Customer Conversation Timeline", "description": "Aggregate email and SMS conversations for a customer into a unified timeline"}, {"type": "mcp", "id": "da8a2235-1343-4428-bb95-63fa63d5d7c4", "mcp_name": "SDR Management", "updated_at": "2025-09-01T09:49:51.191694", "category": "sales", "name": "Fetching Metrics", "description": "Retrieve metrics from the 'metrics' collection filtered by user, campaign, date range, and grouped by a period (day, week, month)."}, {"type": "mcp", "id": "cb33feb8-1d13-4b54-aa0f-ff11ae7efbd2", "mcp_name": "Flux-image", "updated_at": "2025-09-01T11:41:02.926418", "category": "general", "name": "generateImage", "description": "Generate an image, return the base64-encoded data, and save to a file by default"}, {"type": "mcp", "id": "cb33feb8-1d13-4b54-aa0f-ff11ae7efbd2", "mcp_name": "Flux-image", "updated_at": "2025-09-01T11:41:02.926418", "category": "general", "name": "listImageModels", "description": "List available image models"}, {"type": "mcp", "id": "cb33feb8-1d13-4b54-aa0f-ff11ae7efbd2", "mcp_name": "Flux-image", "updated_at": "2025-09-01T11:41:02.926418", "category": "general", "name": "generateImageUrl", "description": "Generate an image URL from a text prompt"}, {"type": "mcp", "id": "d00cfd0e-67ec-4dbd-9a9b-fcec170fd2b4", "mcp_name": "mcp-fetch", "updated_at": "2025-08-04T05:16:42.552716", "category": "general", "name": "fetch", "description": "Retrieves URLs from the Internet and extracts their content as markdown. If images are found, their URLs will be included in the response."}, {"type": "mcp", "id": "035a8924-5153-4133-940e-ac0be0dbd32a", "mcp_name": "DuckDuckGo", "updated_at": "2025-08-25T12:07:39.889607", "category": "general", "name": "search", "description": "\n    Search DuckDuckGo and return formatted results.\n\n    Args:\n        query: The search query string\n        max_results: Maximum number of results to return (default: 10)\n        ctx: MCP context for logging\n    "}, {"type": "mcp", "id": "035a8924-5153-4133-940e-ac0be0dbd32a", "mcp_name": "DuckDuckGo", "updated_at": "2025-08-25T12:07:39.889607", "category": "general", "name": "fetch_content", "description": "\n    Fetch and parse content from a webpage URL.\n\n    Args:\n        url: The webpage URL to fetch content from\n        ctx: MCP context for logging\n    "}, {"type": "mcp", "id": "7dffae28-d271-4b75-acee-f738dcc26b72", "mcp_name": "Tavily Search 2.0", "updated_at": "2025-08-18T10:07:20.467913", "category": "general", "name": "tavily-search", "description": "A powerful web search tool that provides comprehensive, real-time results using Tavily's AI search engine. Returns relevant web content with customizable parameters for result count, content type, and domain filtering. Ideal for gathering current information, news, and detailed web content analysis."}, {"type": "mcp", "id": "7dffae28-d271-4b75-acee-f738dcc26b72", "mcp_name": "Tavily Search 2.0", "updated_at": "2025-08-18T10:07:20.467913", "category": "general", "name": "tavily-extract", "description": "A powerful web content extraction tool that retrieves and processes raw content from specified URLs, ideal for data collection, content analysis, and research tasks."}, {"type": "mcp", "id": "7dffae28-d271-4b75-acee-f738dcc26b72", "mcp_name": "Tavily Search 2.0", "updated_at": "2025-08-18T10:07:20.467913", "category": "general", "name": "tavily-crawl", "description": "A powerful web crawler that initiates a structured web crawl starting from a specified base URL. The crawler expands from that point like a tree, following internal links across pages. You can control how deep and wide it goes, and guide it to focus on specific sections of the site."}, {"type": "mcp", "id": "7dffae28-d271-4b75-acee-f738dcc26b72", "mcp_name": "Tavily Search 2.0", "updated_at": "2025-08-18T10:07:20.467913", "category": "general", "name": "tavily-map", "description": "A powerful web mapping tool that creates a structured map of website URLs, allowing you to discover and analyze site structure, content organization, and navigation paths. Perfect for site audits, content discovery, and understanding website architecture."}, {"type": "mcp", "id": "0636e3f1-211b-4f36-aae5-a6aec0b963e0", "mcp_name": "<PERSON><PERSON>", "updated_at": "2025-08-21T12:23:20.262172", "category": "sales", "name": "single_phone_call", "description": "Initiate a phone outbound call using phone-based agent lookup.\n\nThis endpoint allows making outbound calls without demo call restrictions.\nRequires valid phone numbers and proper user authentication via public key.\n\nArgs:\n    from_phone: Originating phone number (used to lookup agent). Must be a valid phone number format.\n    to_phone: Destination phone number. Must be a valid phone number format.\n    public_key: Public key for user authentication (required). Must be a valid public key.\n    metadata: Optional metadata for the call (JSON string). Can contain additional call context or parameters.\n\nReturns:\n    JSON string containing the call result with success status, message, and call_id if successful."}, {"type": "mcp", "id": "0636e3f1-211b-4f36-aae5-a6aec0b963e0", "mcp_name": "<PERSON><PERSON>", "updated_at": "2025-08-21T12:23:20.262172", "category": "sales", "name": "phone_call_with_system_message", "description": "Initiate a node outbound call with system message template processing.\n\nProcesses system_message template with metadata placeholders and includes it in room metadata.\nThe server-side API handles template processing (replacing placeholders like {name}, {phoneNumber}\nwith values from metadata dictionary).\n\nArgs:\n    from_phone: Originating phone number (used to lookup agent). Must be a valid phone number format.\n    to_phone: Destination phone number. Must be a valid phone number format.\n    system_message: System message template with placeholders (e.g., 'Hello {name}, your phone is {phoneNumber}').\n    metadata: Metadata dictionary for template placeholder replacement (e.g., {\"name\": \"<PERSON>\", \"phoneNumber\": \"************\"}).\n    public_key: Public key for user authentication (required). Must be a valid public key.\n\nReturns:\n    JSON string containing the call result with success status, message, call_id, and processed_system_message if successful."}, {"type": "mcp", "id": "0dc83245-794f-405d-8814-7771260d3c60", "mcp_name": "Script Generation", "updated_at": "2025-07-25T12:21:58.170822", "category": "general", "name": "script_generate", "description": "Provide topic and keyword to generator <PERSON><PERSON><PERSON>"}, {"type": "mcp", "id": "0dc83245-794f-405d-8814-7771260d3c60", "mcp_name": "Script Generation", "updated_at": "2025-07-25T12:21:58.170822", "category": "general", "name": "research", "description": "Research for the given topic"}, {"type": "mcp", "id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "mcp_name": "Desktop Commander", "updated_at": "2025-08-15T08:20:19.786291", "category": "general", "name": "get_config", "description": "\n                        Get the complete server configuration as JSON. Config includes fields for:\n                        - blockedCommands (array of blocked shell commands)\n                        - defaultShell (shell to use for commands)\n                        - allowedDirectories (paths the server can access)\n                        - fileReadLineLimit (max lines for read_file, default 1000)\n                        - fileWriteLineLimit (max lines per write_file call, default 50)\n                        - telemetryEnabled (boolean for telemetry opt-in/out)\n                        -  version (version of the DesktopCommander)\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions."}, {"type": "mcp", "id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "mcp_name": "Desktop Commander", "updated_at": "2025-08-15T08:20:19.786291", "category": "general", "name": "read_file", "description": "\n                        Read the contents of a file from the file system or a URL with optional offset and length parameters.\n                        \n                        Prefer this over 'execute_command' with cat/type for viewing files.\n                        \n                        Supports partial file reading with:\n                        - 'offset' (start line, default: 0)\n                          * Positive: Start from line N (0-based indexing)\n                          * Negative: Read last N lines from end (tail behavior)\n                        - 'length' (max lines to read, default: configurable via 'fileReadLineLimit' setting, initially 1000)\n                          * Used with positive offsets for range reading\n                          * Ignored when offset is negative (reads all requested tail lines)\n                        \n                        Examples:\n                        - offset: 0, length: 10     → First 10 lines\n                        - offset: 100, length: 5    → Lines 100-104\n                        - offset: -20               → Last 20 lines  \n                        - offset: -5, length: 10    → Last 5 lines (length ignored)\n                        \n                        Performance optimizations:\n                        - Large files with negative offsets use reverse reading for efficiency\n                        - Large files with deep positive offsets use byte estimation\n                        - Small files use fast readline streaming\n                        \n                        When reading from the file system, only works within allowed directories.\n                        Can fetch content from URLs when isUrl parameter is set to true\n                        (URLs are always read in full regardless of offset/length).\n                        \n                        Handles text files normally and image files are returned as viewable images.\n                        Recognized image types: PNG, JPEG, GIF, WebP.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions."}, {"type": "mcp", "id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "mcp_name": "Desktop Commander", "updated_at": "2025-08-15T08:20:19.786291", "category": "general", "name": "read_multiple_files", "description": "\n                        Read the contents of multiple files simultaneously.\n                        \n                        Each file's content is returned with its path as a reference.\n                        Handles text files normally and renders images as viewable content.\n                        Recognized image types: PNG, JPEG, GIF, WebP.\n                        \n                        Failed reads for individual files won't stop the entire operation.\n                        Only works within allowed directories.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions."}, {"type": "mcp", "id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "mcp_name": "Desktop Commander", "updated_at": "2025-08-15T08:20:19.786291", "category": "general", "name": "move_file", "description": "\n                        Move or rename files and directories.\n                        \n                        Can move files between directories and rename them in a single operation.\n                        Both source and destination must be within allowed directories.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions."}, {"type": "mcp", "id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "mcp_name": "Desktop Commander", "updated_at": "2025-08-15T08:20:19.786291", "category": "general", "name": "get_file_info", "description": "\n                        Retrieve detailed metadata about a file or directory including:\n                        - size\n                        - creation time\n                        - last modified time \n                        - permissions\n                        - type\n                        - lineCount (for text files)\n                        - lastLine (zero-indexed number of last line, for text files)\n                        - appendPosition (line number for appending, for text files)\n                        \n                        Only works within allowed directories.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions."}, {"type": "mcp", "id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "mcp_name": "Desktop Commander", "updated_at": "2025-08-15T08:20:19.786291", "category": "general", "name": "execute_command", "description": "\n                        Execute a terminal command with timeout.\n                        \n                        Command will continue running in background if it doesn't complete within timeout.\n                        \n                        NOTE: For file operations, prefer specialized tools like read_file, search_code, \n                        list_directory instead of cat, grep, or ls commands.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions."}, {"type": "mcp", "id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "mcp_name": "Desktop Commander", "updated_at": "2025-08-15T08:20:19.786291", "category": "general", "name": "read_output", "description": "\n                        Read new output from a running terminal session.\n                        Set timeout_ms for long running commands.\n                        \n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions."}, {"type": "mcp", "id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "mcp_name": "Desktop Commander", "updated_at": "2025-08-15T08:20:19.786291", "category": "general", "name": "force_terminate", "description": "\n                        Force terminate a running terminal session.\n                        \n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions."}, {"type": "mcp", "id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "mcp_name": "Desktop Commander", "updated_at": "2025-08-15T08:20:19.786291", "category": "general", "name": "list_sessions", "description": "\n                        List all active terminal sessions.\n                        \n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions."}, {"type": "mcp", "id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "mcp_name": "Desktop Commander", "updated_at": "2025-08-15T08:20:19.786291", "category": "general", "name": "list_processes", "description": "\n                        List all running processes.\n                        \n                        Returns process information including PID, command name, CPU usage, and memory usage.\n                        \n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions."}, {"type": "mcp", "id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "mcp_name": "Desktop Commander", "updated_at": "2025-08-15T08:20:19.786291", "category": "general", "name": "kill_process", "description": "\n                        Terminate a running process by PID.\n                        \n                        Use with caution as this will forcefully terminate the specified process.\n                        \n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions."}, {"type": "mcp", "id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "mcp_name": "Desktop Commander", "updated_at": "2025-08-15T08:20:19.786291", "category": "general", "name": "create_directory", "description": "\n                        Create a new directory or ensure a directory exists.\n                        \n                        Can create multiple nested directories in one operation.\n                        Only works within allowed directories.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions."}, {"type": "mcp", "id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "mcp_name": "Desktop Commander", "updated_at": "2025-08-15T08:20:19.786291", "category": "general", "name": "write_file", "description": "\n                        Write or append to file contents. \n\n                        🎯 CHUNKING IS STANDARD PRACTICE: Always write files in chunks of 25-30 lines maximum.\n                        This is the normal, recommended way to write files - not an emergency measure.\n\n                        STANDARD PROCESS FOR ANY FILE:\n                        1. FIRST → write_file(filePath, firstChunk, {mode: 'rewrite'})  [≤30 lines]\n                        2. THEN → write_file(filePath, secondChunk, {mode: 'append'})   [≤30 lines]\n                        3. CONTINUE → write_file(filePath, nextChunk, {mode: 'append'}) [≤30 lines]\n\n                        ⚠️ ALWAYS CHUNK PROACTIVELY - don't wait for performance warnings!\n\n                        WHEN TO CHUNK (always be proactive):\n                        1. Any file expected to be longer than 25-30 lines\n                        2. When writing multiple files in sequence\n                        3. When creating documentation, code files, or configuration files\n                        \n                        HANDLING CONTINUATION (\"Continue\" prompts):\n                        If user asks to \"Continue\" after an incomplete operation:\n                        1. Read the file to see what was successfully written\n                        2. Continue writing ONLY the remaining content using {mode: 'append'}\n                        3. Keep chunks to 25-30 lines each\n                        \n                        Files over 50 lines will generate performance notes but are still written successfully.\n                        Only works within allowed directories.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions."}, {"type": "mcp", "id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "mcp_name": "Desktop Commander", "updated_at": "2025-08-15T08:20:19.786291", "category": "general", "name": "edit_block", "description": "\n                        Apply surgical text replacements to files.\n                        \n                        BEST PRACTICE: Make multiple small, focused edits rather than one large edit.\n                        Each edit_block call should change only what needs to be changed - include just enough \n                        context to uniquely identify the text being modified.\n                        \n                        Takes:\n                        - file_path: Path to the file to edit\n                        - old_string: Text to replace\n                        - new_string: Replacement text\n                        - expected_replacements: Optional parameter for number of replacements\n                        \n                        By default, replaces only ONE occurrence of the search text.\n                        To replace multiple occurrences, provide the expected_replacements parameter with\n                        the exact number of matches expected.\n                        \n                        UNIQUENESS REQUIREMENT: When expected_replacements=1 (default), include the minimal\n                        amount of context necessary (typically 1-3 lines) before and after the change point,\n                        with exact whitespace and indentation.\n                        \n                        When editing multiple sections, make separate edit_block calls for each distinct change\n                        rather than one large replacement.\n                        \n                        When a close but non-exact match is found, a character-level diff is shown in the format:\n                        common_prefix{-removed-}{+added+}common_suffix to help you identify what's different.\n                        \n                        Similar to write_file, there is a configurable line limit (fileWriteLineLimit) that warns\n                        if the edited file exceeds this limit. If this happens, consider breaking your edits into\n                        smaller, more focused changes.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions."}, {"type": "mcp", "id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "mcp_name": "Desktop Commander", "updated_at": "2025-08-15T08:20:19.786291", "category": "general", "name": "set_config_value", "description": "\n                        Set a specific configuration value by key.\n                        \n                        WARNING: Should be used in a separate chat from file operations and \n                        command execution to prevent security issues.\n                        \n                        Config keys include:\n                        - blockedCommands (array)\n                        - defaultShell (string)\n                        - allowedDirectories (array of paths)\n                        - fileReadLineLimit (number, max lines for read_file)\n                        - fileWriteLineLimit (number, max lines per write_file call)\n                        - telemetryEnabled (boolean)\n                        \n                        IMPORTANT: Setting allowedDirectories to an empty array ([]) allows full access \n                        to the entire file system, regardless of the operating system.\n                        \n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions."}, {"type": "mcp", "id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "mcp_name": "Desktop Commander", "updated_at": "2025-08-15T08:20:19.786291", "category": "general", "name": "list_directory", "description": "\n                        Get a detailed listing of all files and directories in a specified path.\n                        \n                        Use this instead of 'execute_command' with ls/dir commands.\n                        Results distinguish between files and directories with [FILE] and [DIR] prefixes.\n                        Only works within allowed directories.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions."}, {"type": "mcp", "id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "mcp_name": "Desktop Commander", "updated_at": "2025-08-15T08:20:19.786291", "category": "general", "name": "search_files", "description": "\n                        Finds files by name using a case-insensitive substring matching.\n                        \n                        Use this instead of 'execute_command' with find/dir/ls for locating files.\n                        Searches through all subdirectories from the starting path.\n                        \n                        Has a default timeout of 30 seconds which can be customized using the timeoutMs parameter.\n                        Only searches within allowed directories.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions."}, {"type": "mcp", "id": "d94e4d15-7459-4969-91a9-bbe3aeef08e6", "mcp_name": "Desktop Commander", "updated_at": "2025-08-15T08:20:19.786291", "category": "general", "name": "search_code", "description": "\n                        Search for text/code patterns within file contents using ripgrep.\n                        \n                        Use this instead of 'execute_command' with grep/find for searching code content.\n                        Fast and powerful search similar to VS Code search functionality.\n                        \n                        Supports regular expressions, file pattern filtering, and context lines.\n                        Has a default timeout of 30 seconds which can be customized.\n                        Only searches within allowed directories.\n                        \n                        IMPORTANT: Always use absolute paths (starting with '/' or drive letter like 'C:\\') for reliability. Relative paths may fail as they depend on the current working directory. Tilde paths (~/...) might not work in all contexts. Unless the user explicitly asks for relative paths, use absolute paths.\n                        This command can be referenced as \"DC: ...\" or \"use Desktop Commander to ...\" in your instructions."}, {"type": "mcp", "id": "8bec32f3-5c9b-43ef-ae93-c0a46b7106ec", "mcp_name": "context-engine-mcp", "updated_at": "2025-08-14T05:16:29.692099", "category": "general", "name": "search", "description": "Search for documents semantically similar to a query."}, {"type": "mcp", "id": "8bec32f3-5c9b-43ef-ae93-c0a46b7106ec", "mcp_name": "context-engine-mcp", "updated_at": "2025-08-14T05:16:29.692099", "category": "general", "name": "upload_files_by_urls", "description": "Sync specific Google Drive files by URL."}, {"type": "mcp", "id": "8bec32f3-5c9b-43ef-ae93-c0a46b7106ec", "mcp_name": "context-engine-mcp", "updated_at": "2025-08-14T05:16:29.692099", "category": "general", "name": "search_with_api_key", "description": "Search for documents semantically similar to a query using API key authentication.\n\nArgs:\n    api_key (str): MANDATORY - Valid API key for authentication\n    query_text (str): The search query text\n    top_k (int): Number of results to return (default: 10)\n    agent_id (Optional[str]): Optional agent ID to filter results\n    file_ids (Optional[List[str]]): Optional list of specific file IDs to search within\n    least_score (Optional[float]): Optional minimum score threshold for results"}, {"type": "mcp", "id": "8bec32f3-5c9b-43ef-ae93-c0a46b7106ec", "mcp_name": "context-engine-mcp", "updated_at": "2025-08-14T05:16:29.692099", "category": "general", "name": "upload_files_by_urls_with_api_key", "description": "Sync specific Google Drive files by URL using API key authentication.\n\nArgs:\n    api_key (str): MANDATORY - Valid API key for authentication\n    drive_url (List[str]): List of Google Drive URLs to sync\n    agent_id (str): Agent ID for the sync operation"}, {"type": "mcp", "id": "7924caa0-db5b-484d-bc65-49360cb84c3a", "mcp_name": "Google Calendar", "updated_at": "2025-08-26T12:45:43.547842", "category": "general", "name": "fetch_google_meeting", "description": "Fetch Google Calendar data - either a specific meeting by ID or multiple events with optional time filtering"}, {"type": "mcp", "id": "7924caa0-db5b-484d-bc65-49360cb84c3a", "mcp_name": "Google Calendar", "updated_at": "2025-08-26T12:45:43.547842", "category": "general", "name": "update_google_meeting", "description": "Update an existing Google Calendar event"}, {"type": "mcp", "id": "7924caa0-db5b-484d-bc65-49360cb84c3a", "mcp_name": "Google Calendar", "updated_at": "2025-08-26T12:45:43.547842", "category": "general", "name": "delete_google_meeting", "description": "Delete an existing Google Calendar event"}, {"type": "mcp", "id": "7924caa0-db5b-484d-bc65-49360cb84c3a", "mcp_name": "Google Calendar", "updated_at": "2025-08-26T12:45:43.547842", "category": "general", "name": "validate_google_credentials", "description": "Validate Google OAuth credentials and check API access"}, {"type": "mcp", "id": "7924caa0-db5b-484d-bc65-49360cb84c3a", "mcp_name": "Google Calendar", "updated_at": "2025-08-26T12:45:43.547842", "category": "general", "name": "create_google_meeting", "description": "Create a new Google Calendar event with Google Meet link"}, {"type": "mcp", "id": "2966de79-5ada-41ae-b1b3-118fab5cb946", "mcp_name": "video-generation", "updated_at": "2025-07-11T11:03:42.082100", "category": "general", "name": "generate_video", "description": "generate and process the video"}, {"type": "mcp", "id": "ce1e8436-63b7-4f64-86b0-3cecc3bbd05b", "mcp_name": "script-generation-mcp-server", "updated_at": "2025-08-13T06:19:06.140863", "category": "general", "name": "generate_script", "description": "Generate a script using OpenAI GPT-4o."}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "get_message", "description": "Get a specific message by timestamp"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "list_channels", "description": "List public or pre-defined channels in the workspace with pagination"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "add_reaction", "description": "Add a reaction emoji to a message"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "get_thread_replies", "description": "Get all replies in a message thread"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "get_user_profile", "description": "Get detailed profile information for a specific user"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "get_conversation", "description": "Get information about a conversation/channel"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "create_channel", "description": "Create a new public channel"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "create_private_channel", "description": "Create a new private channel"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "find_public_channel", "description": "Find a public channel by name"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "set_channel_topic", "description": "Set the topic for a channel"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "get_message_permalink", "description": "Get a permalink URL for a specific message"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "edit_message", "description": "Update/edit a message"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "delete_message", "description": "Delete a message"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "find_message", "description": "Search for messages matching a query"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "get_message_by_timestamp", "description": "Get a message by its timestamp"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "retrieve_thread_messages", "description": "Retrieve all messages in a thread"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "find_user_by_email", "description": "Find a user by their email address"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "find_user_by_id", "description": "Find a user by their ID"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "find_user_by_name", "description": "Find a user by their name or username"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "update_profile", "description": "Update user profile"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "set_status", "description": "Set user status"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "send_channel_message", "description": "Send a message to a channel"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "send_direct_message", "description": "Send a direct message to a user"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "send_private_channel_message", "description": "Send a message to a private channel"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "invite_user_to_channel", "description": "Invite a user to a channel"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "remove_user_from_channel", "description": "Remove a user from a channel"}, {"type": "mcp", "id": "0730b975-db31-4861-87c1-216ac6c3c907", "mcp_name": "Slack MCP", "updated_at": "2025-08-29T06:07:18.680140", "category": "general", "name": "add_reminder", "description": "Add a reminder"}, {"type": "mcp", "id": "cde76df3-a879-496a-95f4-8b1f95d81a12", "mcp_name": "stock-image-generation-mcp", "updated_at": "2025-08-31T04:56:53.750056", "category": "marketing", "name": "generate_stock_image", "description": "generate and find the stock image for the video"}, {"type": "mcp", "id": "cde76df3-a879-496a-95f4-8b1f95d81a12", "mcp_name": "stock-image-generation-mcp", "updated_at": "2025-08-31T04:56:53.750056", "category": "marketing", "name": "generate_ai_stock_image", "description": "generate and find the stock image for the video"}, {"type": "mcp", "id": "cde76df3-a879-496a-95f4-8b1f95d81a12", "mcp_name": "stock-image-generation-mcp", "updated_at": "2025-08-31T04:56:53.750056", "category": "marketing", "name": "generate_image", "description": "generate the image using the script"}, {"type": "mcp", "id": "cde76df3-a879-496a-95f4-8b1f95d81a12", "mcp_name": "stock-image-generation-mcp", "updated_at": "2025-08-31T04:56:53.750056", "category": "marketing", "name": "fetch_stock_images", "description": "fetch the stock image using the script"}, {"type": "mcp", "id": "fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4", "mcp_name": "Tavily Web Search and Extraction Server", "updated_at": "2025-08-31T07:18:24.698533", "category": "general", "name": "tavily-extract", "description": "A powerful web content extraction tool that retrieves and processes raw content from specified URLs, ideal for data collection, content analysis, and research tasks."}, {"type": "mcp", "id": "fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4", "mcp_name": "Tavily Web Search and Extraction Server", "updated_at": "2025-08-31T07:18:24.698533", "category": "general", "name": "tavily-crawl", "description": "A powerful web crawler that initiates a structured web crawl starting from a specified base URL. The crawler expands from that point like a tree, following internal links across pages. You can control how deep and wide it goes, and guide it to focus on specific sections of the site."}, {"type": "mcp", "id": "fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4", "mcp_name": "Tavily Web Search and Extraction Server", "updated_at": "2025-08-31T07:18:24.698533", "category": "general", "name": "tavily-search", "description": "A powerful web search tool that provides comprehensive, real-time results using Tavily's AI search engine. Returns relevant web content with customizable parameters for result count, content type, and domain filtering. Ideal for gathering current information, news, and detailed web content analysis."}, {"type": "mcp", "id": "fe0ccd31-8e7c-47d8-a8d0-02ffe7fd34f4", "mcp_name": "Tavily Web Search and Extraction Server", "updated_at": "2025-08-31T07:18:24.698533", "category": "general", "name": "tavily-map", "description": "A powerful web mapping tool that creates a structured map of website URLs, allowing you to discover and analyze site structure, content organization, and navigation paths. Perfect for site audits, content discovery, and understanding website architecture."}, {"type": "mcp", "id": "37db65ab-0586-434e-a58d-7ddc6d9a8beb", "mcp_name": "Gmail", "updated_at": "2025-08-27T11:02:01.789159", "category": "general", "name": "add_label_to_email", "description": "Add a label to an email message"}, {"type": "mcp", "id": "37db65ab-0586-434e-a58d-7ddc6d9a8beb", "mcp_name": "Gmail", "updated_at": "2025-08-27T11:02:01.789159", "category": "general", "name": "archive_email", "description": "Archive an email message"}, {"type": "mcp", "id": "37db65ab-0586-434e-a58d-7ddc6d9a8beb", "mcp_name": "Gmail", "updated_at": "2025-08-27T11:02:01.789159", "category": "general", "name": "delete_email", "description": "Send an email message to the trash"}, {"type": "mcp", "id": "37db65ab-0586-434e-a58d-7ddc6d9a8beb", "mcp_name": "Gmail", "updated_at": "2025-08-27T11:02:01.789159", "category": "general", "name": "create_draft", "description": "Create a draft email message"}, {"type": "mcp", "id": "37db65ab-0586-434e-a58d-7ddc6d9a8beb", "mcp_name": "Gmail", "updated_at": "2025-08-27T11:02:01.789159", "category": "general", "name": "create_draft_reply", "description": "Create a draft reply to an existing email"}, {"type": "mcp", "id": "37db65ab-0586-434e-a58d-7ddc6d9a8beb", "mcp_name": "Gmail", "updated_at": "2025-08-27T11:02:01.789159", "category": "general", "name": "create_label", "description": "Create a new label in Gmail"}, {"type": "mcp", "id": "37db65ab-0586-434e-a58d-7ddc6d9a8beb", "mcp_name": "Gmail", "updated_at": "2025-08-27T11:02:01.789159", "category": "general", "name": "send_email", "description": "Create and send a new email message"}, {"type": "mcp", "id": "37db65ab-0586-434e-a58d-7ddc6d9a8beb", "mcp_name": "Gmail", "updated_at": "2025-08-27T11:02:01.789159", "category": "general", "name": "remove_label_from_email", "description": "Remove a label from an email message"}, {"type": "mcp", "id": "37db65ab-0586-434e-a58d-7ddc6d9a8beb", "mcp_name": "Gmail", "updated_at": "2025-08-27T11:02:01.789159", "category": "general", "name": "remove_label_from_conversation", "description": "Remove a specified label from all emails within a conversation"}, {"type": "mcp", "id": "37db65ab-0586-434e-a58d-7ddc6d9a8beb", "mcp_name": "Gmail", "updated_at": "2025-08-27T11:02:01.789159", "category": "general", "name": "reply_to_email", "description": "Send a reply to an email message"}, {"type": "mcp", "id": "37db65ab-0586-434e-a58d-7ddc6d9a8beb", "mcp_name": "Gmail", "updated_at": "2025-08-27T11:02:01.789159", "category": "general", "name": "find_email", "description": "Find an email message based on search query"}, {"type": "mcp", "id": "37db65ab-0586-434e-a58d-7ddc6d9a8beb", "mcp_name": "Gmail", "updated_at": "2025-08-27T11:02:01.789159", "category": "general", "name": "find_or_send_email", "description": "Find a specific message or create and send a new one if not found"}, {"type": "workflow", "id": "a6afa109-be9f-4213-b6e9-6e24d4713140", "name": "Meeting Scheduler", "category": null, "description": "Meeting_Scheduler", "updated_at": "2025-09-01T09:59:35.359743"}, {"type": "workflow", "id": "19fe0fb6-1e9e-4e6c-9bfa-21b03b42e872", "name": "Meeting Rescheduler", "category": null, "description": "Meeting_Rescheduler", "updated_at": "2025-09-01T09:58:17.977527"}, {"type": "workflow", "id": "92244fba-e6db-47ea-ab4e-eb7aae829af1", "name": "Untitled Workflow", "category": "general", "description": "Untitled_Workflow", "updated_at": "2025-08-28T11:50:56.359279"}, {"type": "workflow", "id": "a54d799b-50bf-444a-9e0a-fea809443054", "name": "SDR Email Reply Workflow (trigger)", "category": null, "description": "SDR_Email_Reply_Workflow_(trigger)", "updated_at": "2025-08-22T10:06:09.469444"}, {"type": "workflow", "id": "e9cc3f2f-e2aa-45b9-a162-0647d296b44e", "name": "SDR Contact Generation Flow", "category": null, "description": "SDR_Contact_Generation_Flow", "updated_at": "2025-08-21T13:02:49.109869"}, {"type": "workflow", "id": "b1a7a223-e304-4f5c-b74c-a0421e8d0fd2", "name": "Screening (<PERSON>)", "category": null, "description": "Screening_(<PERSON>_<PERSON><PERSON>)", "updated_at": "2025-09-01T10:10:00.452143"}, {"type": "workflow", "id": "2a215231-bcff-4741-84b4-b91a1e781abd", "name": "Email Draft workflow", "category": null, "description": "Email_Draft_workflow", "updated_at": "2025-08-31T07:18:24.414796"}, {"type": "workflow", "id": "d55ce613-3f0a-4afa-a4f6-fffa9c942376", "name": "Blog Generation v5.5 - published", "category": "general", "description": "No description provided.", "updated_at": "2025-08-26T07:02:44.312067"}, {"type": "workflow", "id": "b5e46aa2-ec27-4239-a547-af42f8b4375d", "name": "JD Creation - (Agent-<PERSON><PERSON>) ", "category": null, "description": "JD_Creation_-_(<PERSON><PERSON><PERSON><PERSON>)_", "updated_at": "2025-09-01T10:06:31.617730"}, {"type": "workflow", "id": "f907eb17-76f9-4583-80be-6c6bfa757fc6", "name": "GET JDs (Agent-Chat)", "category": null, "description": "GET_JDs_(Agent-Cha<PERSON>)", "updated_at": "2025-08-22T04:49:38.195110"}, {"type": "workflow", "id": "46becf9e-4bbe-435f-bc7f-efce231d8609", "name": "Update Comapany Details", "category": null, "description": "Update_Comapany_Details", "updated_at": "2025-08-22T04:49:50.376310"}, {"type": "workflow", "id": "aabd047c-7968-4c7c-a335-3d5502845751", "name": "Add Candidate", "category": null, "description": "Add_Candidate", "updated_at": "2025-09-01T12:56:59.806921"}, {"type": "workflow", "id": "f40818d6-c05d-441c-b680-cb4eb6f6a524", "name": "JD By ID", "category": null, "description": "JD_By_ID", "updated_at": "2025-08-22T04:50:08.348322"}, {"type": "workflow", "id": "72223fb7-0e89-465c-a477-cd695488cf8d", "name": "Update Job Status", "category": null, "description": "Update_Job_Status", "updated_at": "2025-09-01T13:00:45.300185"}, {"type": "workflow", "id": "bbc002e8-90da-4336-a6cb-c461abd23929", "name": "Blog Generation v5.5", "category": null, "description": "Blog_Generation_v5.5", "updated_at": "2025-08-28T11:38:26.856573"}, {"type": "workflow", "id": "77b47788-94f0-41d9-a89d-430df99428ed", "name": "Issue Review", "category": null, "description": "Issue_Review", "updated_at": "2025-08-22T04:50:43.419676"}, {"type": "workflow", "id": "51729580-e6a7-42b8-843a-fbf0d4a71466", "name": "Google Sheets Row Range Reader v2", "category": "general", "description": "No description provided.", "updated_at": "2025-08-22T04:50:51.098979"}, {"type": "workflow", "id": "b3ebf7a5-151c-4490-a56a-6b8b6f1ce148", "name": "Blog Generation v5.4", "category": "general", "description": "No description provided.", "updated_at": "2025-08-22T04:51:00.161482"}, {"type": "workflow", "id": "1d0c7895-4304-4e49-9d6c-e1623588beea", "name": "Send Issue Review", "category": null, "description": "Send_Issue_Review", "updated_at": "2025-08-22T04:51:15.977717"}, {"type": "workflow", "id": "3625d83b-0888-4016-85ca-dc23c964de58", "name": "Screening", "category": null, "description": "Screening", "updated_at": "2025-09-01T10:09:41.877863"}, {"type": "workflow", "id": "4c170d37-c427-4562-b2f2-400a61e67cf8", "name": "Blog Generation v5.3", "category": null, "description": "Blog_Generation_v5.3", "updated_at": "2025-08-22T04:51:48.315592"}, {"type": "workflow", "id": "80da0480-0dbc-40e0-a444-0bcfa78ba55b", "name": "Blog Generation v5.2", "category": null, "description": "Blog_Generation_v5.2", "updated_at": "2025-08-22T04:52:18.825601"}, {"type": "workflow", "id": "807c0057-3203-4199-af37-74e240547ada", "name": "Google Sheets Row Reader v1", "category": null, "description": "Google_Sheets_Row_Reader_v1", "updated_at": "2025-08-22T04:52:12.049592"}, {"type": "workflow", "id": "34a390ae-d969-43ea-a2e5-aad8e04e97a8", "name": "Google Sheets Row Range Reader v1", "category": null, "description": "Google_Sheets_Row_Range_Reader_v1", "updated_at": "2025-08-22T04:52:44.647224"}, {"type": "workflow", "id": "853393b7-5a81-4658-a992-c9478dc149d6", "name": "Blogs Finder V1", "category": null, "description": "Blogs_Finder_V1", "updated_at": "2025-08-22T04:52:50.537357"}, {"type": "workflow", "id": "03cbe420-1d29-4895-a775-b0cb5c0fb0aa", "name": "Blog Generation v5.1", "category": null, "description": "Blog_Generation_v5.1", "updated_at": "2025-08-22T04:53:52.137711"}, {"type": "workflow", "id": "7798ab31-fb91-49c1-b324-9c249994d059", "name": "Blog Generation v4 (Need Fix)", "category": null, "description": "Blog_Generation_v4_(Need_Fix)", "updated_at": "2025-08-22T04:54:05.943544"}, {"type": "workflow", "id": "ea33e0c3-cf5e-40b3-8865-7b0dce77e045", "name": "Resume Scorer II", "category": null, "description": "Resume_Scorer_II", "updated_at": "2025-09-01T13:17:55.709894"}, {"type": "workflow", "id": "0143feae-68ee-4676-8729-1d4b71728dc7", "name": "Google Forms Responses To Sheets", "category": null, "description": "Google_Forms_Responses_To_Sheets", "updated_at": "2025-08-22T04:54:34.980121"}, {"type": "workflow", "id": "afb6aef3-9848-43fc-9ec5-755a045383e0", "name": "Blog Generation v3", "category": "general", "description": "Blog_Generation_v3", "updated_at": "2025-08-22T04:54:56.109165"}, {"type": "workflow", "id": "1669c87f-2557-425f-a030-1c8f63f7796a", "name": "Clone of Blog Gen v2", "category": null, "description": "Clone_of_Blog_Gen_v2", "updated_at": "2025-08-22T04:55:28.458790"}, {"type": "workflow", "id": "62c10ddb-2bf7-4da4-a8ab-81552dec4962", "name": "VC Automation - Research Workflow - Sheet Based", "category": null, "description": "VC_Automation_-_Research_Workflow_-_Sheet_Based", "updated_at": "2025-08-22T04:55:46.268077"}, {"type": "workflow", "id": "51b51a93-e039-421d-8fd0-e4cbcb90e699", "name": "Blog Generation v2", "category": null, "description": "Blog_Generation_v2", "updated_at": "2025-08-22T04:55:58.578967"}, {"type": "workflow", "id": "c75340d5-60d5-4e3a-93f0-b7d829693817", "name": "SpurRidge Workflow", "category": null, "description": "SpurRidge_Workflow", "updated_at": "2025-08-22T04:56:08.312469"}, {"type": "workflow", "id": "f59993f8-6f92-40b5-861c-410fa82c1114", "name": "Research Workflow", "category": null, "description": "Research_Workflow", "updated_at": "2025-08-22T04:56:18.950204"}, {"type": "workflow", "id": "b7a86377-dab3-49aa-a5d8-00f0ac29a81a", "name": "Profile Research Workflow", "category": null, "description": "Profile_Research_Workflow", "updated_at": "2025-08-22T04:56:30.276593"}, {"type": "workflow", "id": "4a1ba435-cb33-4e3b-9539-9b40ae30aca6", "name": "Company Research Workflow", "category": null, "description": "Company_Research_Workflow", "updated_at": "2025-08-22T04:57:25.917929"}, {"type": "workflow", "id": "fcfa7933-d5fb-4c9c-bcd0-a22254da694d", "name": "Meta ads Create", "category": null, "description": "Meta_ads_Create", "updated_at": "2025-08-22T05:00:04.887332"}, {"type": "workflow", "id": "feb9555a-d851-4f0b-bbb5-bfef9f7bd0f6", "name": "Marketing-Performance-Report-Generators", "category": "automation", "description": "Marketing-Performance-Report-Generators", "updated_at": "2025-08-25T07:09:14.510888"}, {"type": "workflow", "id": "ffa9a8d3-d833-4621-b580-983196cec30e", "name": "JD Creation", "category": null, "description": "JD_Creation", "updated_at": "2025-09-01T10:06:56.840519"}, {"type": "workflow", "id": "4be3afad-64d2-4a93-bdf3-f708ae9a335d", "name": "VC Automation - Email Generator Workflow - Sheet Based", "category": null, "description": "VC_Automation_-_Email_Generator_Workflow_-_Sheet_Based", "updated_at": "2025-08-22T05:00:12.899771"}, {"type": "workflow", "id": "4fdde5aa-6911-4bda-8123-f94e36e3afed", "name": "Website Generator", "category": null, "description": "Website_Generator", "updated_at": "2025-08-25T11:56:24.901012"}, {"type": "workflow", "id": "18d5a6b9-c0e1-4e79-9300-daec6bf6e13a", "name": "Blog Gen 1", "category": null, "description": "Blog_Gen_1", "updated_at": "2025-08-22T05:00:37.027771"}, {"type": "workflow", "id": "9ee4badd-3963-40b6-a59e-572f3c829980", "name": "PPT Generation", "category": null, "description": "PPT_Generation", "updated_at": "2025-08-26T06:09:54.194157"}, {"type": "workflow", "id": "3f014d21-f253-46bc-9a29-b87e6d30a967", "name": "Video Generation -<PERSON><PERSON><PERSON>", "category": null, "description": "Video_Generation_-<PERSON><PERSON><PERSON>", "updated_at": "2025-08-27T11:02:01.697840"}, {"type": "workflow", "id": "32bc4198-825b-44b4-b43e-2b59c3d3e1dd", "name": "Ruh_Video_Generation", "category": null, "description": "Ruh_Video_Generation", "updated_at": "2025-08-22T05:01:51.151149"}, {"type": "workflow", "id": "3cb41a82-1629-4082-8e09-e03e17424e22", "name": "Ciny_Video_generation", "category": null, "description": "Ciny_Video_generation", "updated_at": "2025-08-22T05:01:59.102604"}, {"type": "workflow", "id": "355a99de-e7ce-4741-bf29-bb1f37e52423", "name": "Agentic_component", "category": null, "description": "Agentic_component", "updated_at": "2025-08-22T05:02:03.615495"}, {"type": "workflow", "id": "6ef72d4e-1dd6-4fa9-ac31-33ffe4a85909", "name": "Candidate_api_request", "category": null, "description": "Candidate_api_request", "updated_at": "2025-08-25T12:07:39.806120"}, {"type": "workflow", "id": "b5551f78-e086-4ee2-aed5-92d504991724", "name": "script_audio_generation", "category": null, "description": "script_audio_generation", "updated_at": "2025-08-25T20:53:51.204717"}]