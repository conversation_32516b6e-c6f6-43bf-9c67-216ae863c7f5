{"cells": [{"cell_type": "code", "execution_count": 16, "id": "75021138", "metadata": {}, "outputs": [], "source": ["import requests"]}, {"cell_type": "code", "execution_count": 17, "id": "7f415f7f", "metadata": {}, "outputs": [], "source": ["def context_component(data):\n", "    url = \"https://app-dev.rapidinnovation.dev/api/v1/components?refresh=true\"\n", "    components = requests.get(url).json()\n", "    components = components[data[\"category\"]]\n", "    component = components[data[\"name\"]]\n", "    name = component[\"name\"]\n", "    description = component[\"description\"]\n", "    inputs = component[\"inputs\"]\n", "    outputs = component[\"outputs\"]\n", "    context = \"\"\n", "    context += f\"Name : {name}\\nDescription : {description}\\nOriginalType : {name}\\nType : Component\\n\"\n", "    context += \"Inputs :-\\n\"\n", "    for i in inputs:\n", "        context += f\"Input Name : {i['name']}\\nInput Info : {i['info']}\\nInput Type : {i['input_type']}\\n\"\n", "        if i[\"input_types\"]:\n", "            context += \"Input Types : \" + \", \".join(i[\"input_types\"]) + \"\\n\"\n", "        if i[\"required\"]:\n", "            context += \"Required\\n\"\n", "        if i[\"is_handle\"]:\n", "            context += \"Handle\\n\"\n", "        if i[\"is_list\"]:\n", "            context += \"List\\n\"\n", "        if i[\"real_time_refresh\"]:\n", "            context += \"Real Time Refresh\\n\"\n", "        if i[\"advanced\"]:\n", "            context += \"Advanced\\n\"\n", "        if i[\"value\"]:\n", "            context += f\"Default Value : {i['value']}\\n\"\n", "        if i[\"options\"] is not None:\n", "            context += \"Options : \" + \", \".join(i[\"options\"]) + \"\\n\"\n", "        context += \"\\n\"\n", "    context += \"Outputs :-\\n\"\n", "    for o in outputs:\n", "        context += f\"Output Name : {o['name']}\\nOutput Type : {o['output_type']}\\n\"\n", "        if o[\"semantic_type\"]:\n", "            context += f\"Semantic Type : {o['semantic_type']}\\n\"\n", "        if o[\"method\"]:\n", "            context += f\"Method : {o['method']}\\n\"\n", "        context += \"\\n\"\n", "    return context"]}, {"cell_type": "code", "execution_count": 18, "id": "152e9951", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Name : AgenticAI\n", "Description : Executes an AI agent with tools and memory using AutoGen.\n", "OriginalType : AgenticAI\n", "Type : Component\n", "Inputs :-\n", "Input Name : model_provider\n", "Input Info : The AI model provider to use.\n", "Input Type : dropdown\n", "Default Value : OpenAI\n", "Options : OpenAI, Azure OpenAI, Anthropic, Claude, Google, Gemini, Mistral, Ollama, Custom\n", "\n", "Input Name : base_url\n", "Input Info : Base URL for the API (leave empty for default provider URL).\n", "Input Type : string\n", "\n", "Input Name : model_name\n", "Input Info : Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.\n", "Input Type : dropdown\n", "Default Value : gpt-4o\n", "Options : gpt-4o, gpt-4o-mini, gpt-4-turbo, gpt-4, gpt-3.5-turbo, claude-3-5-sonnet-20241022, claude-3-5-haiku-20241022, claude-3-opus-20240229, claude-3-sonnet-20240229, claude-3-haiku-20240307, claude-2.1, claude-2.0, gemini-1.5-pro, gemini-1.5-flash, gemini-pro, gemini-pro-vision, mistral-large-latest, mistral-medium-latest, mistral-small-latest, open-mistral-7b, open-mixtral-8x7b, open-mixtral-8x22b, llama3.2, llama3.1, llama3, llama2, mistral, mixtral, phi3, gemma, codellama, qwen2\n", "\n", "Input Name : temperature\n", "Input Info : Controls randomness: 0 is deterministic, higher values are more random.\n", "Input Type : float\n", "Default Value : 0.7\n", "\n", "Input Name : description\n", "Input Info : Description of the agent for UI display.\n", "Input Type : string\n", "\n", "Input Name : execution_type\n", "Input Info : Agent execution mode (response only).\n", "Input Type : dropdown\n", "Advanced\n", "Default Value : response\n", "Options : response\n", "\n", "Input Name : query\n", "Input Info : The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.\n", "Input Type : string\n", "Input Types : string, Any\n", "Required\n", "<PERSON><PERSON>\n", "\n", "Input Name : system_message\n", "Input Info : System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly.\n", "Input Type : multiline\n", "Input Types : string, Any\n", "<PERSON><PERSON>\n", "\n", "Input Name : termination_condition\n", "Input Info : Defines when multi-turn conversations should end. Required for interactive execution type.\n", "Input Type : string\n", "\n", "Input Name : max_tokens\n", "Input Info : Maximum response length in tokens.\n", "Input Type : int\n", "Default Value : 1000\n", "\n", "Input Name : input_variables\n", "Input Info : Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.\n", "Input Type : dict\n", "Input Types : dict, Any\n", "<PERSON><PERSON>\n", "\n", "Input Name : tools\n", "Input Info : Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.\n", "Input Type : handle\n", "Input Types : Any\n", "<PERSON><PERSON>\n", "\n", "Input Name : memory\n", "Input Info : Connect a memory object from another node.\n", "Input Type : handle\n", "Input Types : Any\n", "<PERSON><PERSON>\n", "\n", "Input Name : autogen_agent_type\n", "Input Info : The type of AutoGen agent to create internally.\n", "Input Type : dropdown\n", "Advanced\n", "Default Value : Assistant\n", "Options : Assistant, UserProxy, CodeExecutor\n", "\n", "Outputs :-\n", "Output Name : final_answer\n", "Output Type : string\n", "\n", "Output Name : intermediate_steps\n", "Output Type : list\n", "\n", "Output Name : updated_memory\n", "Output Type : Memory\n", "\n", "Output Name : error\n", "Output Type : str\n", "\n", "\n"]}], "source": ["demo = {'category': 'AI',\n", "                           'description': 'Executes an AI agent with tools and '\n", "                                          'memory using AutoGen.',\n", "                           'name': 'AgenticAI',\n", "                           'type': 'component'}\n", "print(context_component(demo))"]}, {"cell_type": "code", "execution_count": 19, "id": "6291432f", "metadata": {}, "outputs": [], "source": ["def workflow_context(data):\n", "    url = \"https://app-dev.rapidinnovation.dev/api/v1/marketplace/workflows/\"+data[\"id\"]\n", "    workflow = requests.get(url).json()\n", "    workflow = workflow[\"workflow\"]\n", "    name = workflow[\"name\"]\n", "    description = workflow[\"description\"]\n", "    inputs = workflow[\"start_nodes\"]\n", "    outputs = [\n", "            {\n", "              \"name\": \"execution_status\",\n", "              \"display_name\": \"Execution Status\",\n", "              \"output_type\": \"string\"\n", "            },\n", "            {\n", "              \"name\": \"workflow_execution_id\",\n", "              \"display_name\": \"Execution ID\",\n", "              \"output_type\": \"string\"\n", "            },\n", "            {\n", "              \"name\": \"message\",\n", "              \"display_name\": \"Message\",\n", "              \"output_type\": \"string\"\n", "            }\n", "          ]\n", "    context = \"\"\n", "    context += f\"Name : {name}\\nDescription : {description}\\nOriginalType : workflow-{data[\"id\"]}\\nType : Workflow\\n\"\n", "    context += \"Inputs :-\\n\"\n", "    for i in inputs:\n", "        context += f\"Input Name : {i['field']}\\nInput Info : {i['field']}\\nInput Type : {i['type']}\\n\"\n", "        context += \"Required\\n\"\n", "        context += \"Handle\\n\"\n", "        context += \"\\n\"\n", "    context += \"Outputs :-\\n\"\n", "    for o in outputs:\n", "        context += f\"Output Name : {o['name']}\\nOutput Type : {o['output_type']}\\n\"\n", "        context += \"\\n\"\n", "    return context"]}, {"cell_type": "code", "execution_count": 20, "id": "ff9de63b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Name : JD Creation - (Agent-Cha<PERSON>) \n", "Description : JD_Creation_-_(<PERSON>-<PERSON><PERSON>)_\n", "OriginalType : workflow-b5e46aa2-ec27-4239-a547-af42f8b4375d\n", "Type : Workflow\n", "Inputs :-\n", "Input Name : query\n", "Input Info : query\n", "Input Type : string\n", "Required\n", "<PERSON><PERSON>\n", "\n", "Outputs :-\n", "Output Name : execution_status\n", "Output Type : string\n", "\n", "Output Name : workflow_execution_id\n", "Output Type : string\n", "\n", "Output Name : message\n", "Output Type : string\n", "\n", "\n"]}], "source": ["demo = {'description': 'JD_Creation_-_(<PERSON>-<PERSON><PERSON>)_',\n", "                           'id': 'b5e46aa2-ec27-4239-a547-af42f8b4375d',\n", "                           'name': '<PERSON><PERSON> Creation - (<PERSON>-<PERSON><PERSON>) ',\n", "                           'type': 'workflow',\n", "                           'updated_at': '2025-09-01T10:06:31.617730'}\n", "print(workflow_context(demo))"]}, {"cell_type": "code", "execution_count": 21, "id": "fe15a357", "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "def normalize_name(name: str) -> str:\n", "    # Replace spaces and hyphens with underscores\n", "    name = re.sub(r\"[\\s\\-]+\", \"_\", name)\n", "    # Collapse multiple underscores\n", "    name = re.sub(r\"_+\", \"_\", name)\n", "    # Strip leading/trailing underscores\n", "    name = name.strip(\"_\")\n", "    return name"]}, {"cell_type": "code", "execution_count": 22, "id": "d970d747", "metadata": {}, "outputs": [], "source": ["def mcp_context(data):\n", "    url = \"https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps/\"+data[\"id\"]\n", "    mcp = requests.get(url).json()\n", "    tools = mcp[\"mcp\"][\"mcp_tools_config\"][\"tools\"]\n", "    for tool in tools:\n", "        if tool[\"name\"] == data[\"name\"]:\n", "            break\n", "    mcp_name = mcp[\"mcp\"][\"name\"]\n", "    name = tool[\"name\"]\n", "    description = tool[\"description\"]\n", "    input_schema = tool[\"input_schema\"]\n", "    output_schema = tool[\"output_schema\"]\n", "    original_type = \"MCP_\"+normalize_name(f\"{mcp_name} - {name}\")\n", "    context = \"\"\n", "    context += f\"Name : {name}\\nDescription : {description}\\nOriginalType : {original_type}\\nType : MCP\\nMCP_id : {data['id']}\\n\"\n", "    context += \"Inputs :-\\n\"\n", "    required_input = input_schema[\"required\"]\n", "    defs = input_schema.get(\"$defs\", {})\n", "    properties = input_schema[\"properties\"]\n", "    for i in properties:\n", "        property_details = properties[i]\n", "        context += f\"Input Name : {i}\\n\"\n", "        if property_details.get(\"description\"):\n", "            context += f\"Input Info : {property_details['description']}\\n\"\n", "        property_scheme = None\n", "        items = None\n", "        options = None\n", "        is_array = False\n", "        if \"type\" in property_details:\n", "            context += f\"Input Type : {property_details['type']}\\n\"\n", "            if property_details[\"type\"] == \"object\":\n", "                property_scheme = property_details[\"properties\"]\n", "            if property_details[\"type\"] == \"array\":\n", "                items = property_details[\"items\"]\n", "                is_array = True\n", "        elif \"anyOf\" in property_details:\n", "            context += \"Input Type : \"\n", "            for j in property_details[\"anyOf\"]:\n", "                if \"type\" in j and j[\"type\"] != \"null\":\n", "                    context += j[\"type\"] + \"\\n\"\n", "                    if j[\"type\"] == \"object\":\n", "                        property_scheme = j[\"properties\"]\n", "                    if j[\"type\"] == \"array\":\n", "                        items = j[\"items\"]\n", "                        is_array = True\n", "                elif \"$ref\" in j:\n", "                    ref = j[\"$ref\"].split(\"/\")[-1]\n", "                    ref = defs[ref]\n", "                    context += ref[\"type\"] + \"\\n\"\n", "                    if \"enum\" in ref:\n", "                        options = ref[\"enum\"]\n", "                    if ref[\"type\"] == \"object\":\n", "                        property_scheme = ref[\"properties\"]\n", "                    if ref[\"type\"] == \"array\":\n", "                        items = ref[\"items\"]\n", "                        is_array = True\n", "        elif \"$ref\" in property_details:\n", "            ref = property_details[\"$ref\"].split(\"/\")[-1]\n", "            ref = defs[ref]\n", "            context += f\"Input Type : {ref['type']}\\n\"\n", "            if \"enum\" in ref:\n", "                options = ref[\"enum\"]\n", "            if ref[\"type\"] == \"object\":\n", "                property_scheme = ref[\"properties\"]\n", "            if ref[\"type\"] == \"array\":\n", "                items = ref[\"items\"]\n", "                is_array = True\n", "        if i in required_input:\n", "            context += \"Required\\n\"\n", "        context += \"Handle\\n\"\n", "        if is_array:\n", "            context += \"List\\n\"\n", "        if property_details.get(\"default\"):\n", "            context += f\"Default Value : {property_details['default']}\\n\"\n", "        if options:\n", "            context += \"Options : \" + \", \".join(options) + \"\\n\"\n", "        if property_scheme:\n", "            context += \"Properties :-\\n\"\n", "            for j in property_scheme:\n", "                context += f\"> Property Name : {j}\\n\"\n", "                if property_scheme[j].get(\"description\"):\n", "                    context += f\"> Property Info : {property_scheme[j]['description']}\\n\"\n", "                if property_scheme[j].get(\"type\"):\n", "                    context += f\"> Property Type : {property_scheme[j]['type']}\\n\"\n", "                if property_scheme[j].get(\"anyOf\"):\n", "                    context += \"> Property Type : \"\n", "                    for k in property_scheme[j][\"anyOf\"]:\n", "                        if \"type\" in k and k[\"type\"] != \"null\":\n", "                            context += k[\"type\"] + \", \"\n", "                    context += \"\\n\"\n", "                if property_scheme[j].get(\"default\"):\n", "                    context += f\"> Property Default Value : {property_scheme[j]['default']}\\n\"\n", "                if property_scheme[j].get(\"enum\"):\n", "                    context += \"> Property Options : \" + \", \".join(property_scheme[j][\"enum\"]) + \"\\n\"\n", "                context += \"> \\n\"\n", "        if items:\n", "            context += f\"Items : {items}\\n\"\n", "        context += \"\\n\"\n", "    context += \"Outputs :-\\n\"\n", "    for o in output_schema[\"properties\"]:\n", "        context += f\"Output Name : {o}\\n\"\n", "        if output_schema[\"properties\"][o].get(\"description\"):\n", "            context += f\"Output Info : {output_schema['properties'][o]['description']}\\n\"\n", "        if output_schema[\"properties\"][o].get(\"type\"):\n", "            context += f\"Output Type : {output_schema['properties'][o]['type']}\\n\"\n", "        context += \"\\n\"\n", "    return context"]}, {"cell_type": "code", "execution_count": 23, "id": "ddbc06e6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Name : script_generate\n", "Description : Provide topic and keyword to generator <PERSON><PERSON><PERSON>\n", "OriginalType : MCP_script_generation_script_generate\n", "Type : MCP\n", "MCP_id : a1700776-e64f-4270-9e4e-3f7a85383919\n", "Inputs :-\n", "Input Name : topic\n", "Input Type : string\n", "Required\n", "<PERSON><PERSON>\n", "\n", "Input Name : script_type\n", "Input Type : string\n", "<PERSON><PERSON>\n", "Default Value : TOPIC\n", "Options : VIDEO, TOPIC, SCRIPT, BLOG, AI\n", "\n", "Input Name : keywords\n", "Input Type : object\n", "<PERSON><PERSON>\n", "Properties :-\n", "> Property Name : time\n", "> Property Type : string, \n", "> \n", "> Property Name : objective\n", "> Property Type : string, \n", "> \n", "> Property Name : audience\n", "> Property Type : string, \n", "> \n", "> Property Name : gender\n", "> Property Type : string, \n", "> \n", "> Property Name : tone\n", "> Property Type : string, \n", "> \n", "> Property Name : speakers\n", "> Property Type : array, \n", "> \n", "\n", "Input Name : video_type\n", "Input Type : string\n", "<PERSON><PERSON>\n", "Default Value : SHORT\n", "Options : SHORT, LONG\n", "\n", "Input Name : link\n", "Input Type : string\n", "<PERSON><PERSON>\n", "\n", "Outputs :-\n", "Output Name : title\n", "Output Info : Title of the generated script\n", "Output Type : string\n", "\n", "Output Name : script\n", "Output Info : The generated script\n", "Output Type : string\n", "\n", "Output Name : script_type\n", "Output Info : Type of the script\n", "Output Type : string\n", "\n", "Output Name : video_type\n", "Output Info : The type of video\n", "Output Type : string\n", "\n", "Output Name : link\n", "Output Info : Optional link for the script\n", "Output Type : string\n", "\n", "\n"]}], "source": ["demo = {'category': 'marketing',\n", "                           'description': 'Provide topic and keyword to '\n", "                                          'generator Script',\n", "                           'id': 'a1700776-e64f-4270-9e4e-3f7a85383919',\n", "                           'mcp_name': 'script generation',\n", "                           'name': 'script_generate',\n", "                           'type': 'mcp',\n", "                           'updated_at': '2025-07-14T11:42:37.361221'}\n", "print(mcp_context(demo))"]}, {"cell_type": "code", "execution_count": null, "id": "fff8be80", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}