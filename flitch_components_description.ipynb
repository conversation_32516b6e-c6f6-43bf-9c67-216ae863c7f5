import json
import os

with open("data retrival/components.json", "r") as f:
    components = json.load(f)

description = []
for cat in components.keys():
    for comp in components[cat].keys():
        loop_description= {}
        loop_description["type"] = "component"
        loop_description["category"] = cat
        loop_description["name"] = components[cat][comp]["name"]
        loop_description["description"] = components[cat][comp]["description"]
        description.append(loop_description)

description_mcps = []
mcps = os.listdir("data retrival/mcps")
for mcp in mcps:
    with open(f"data retrival/mcps/{mcp}", "r") as f:
        mcp_data = json.load(f)
    mcp_id = mcp_data["mcp"]["id"]
    mcp_name = mcp_data["mcp"]["name"]
    mcp_category = mcp_data["mcp"]["category"]
    mcp_updated_at = mcp_data["mcp"]["updated_at"]
    tools = mcp_data["mcp"]["mcp_tools_config"]["tools"]
    for tool in tools:
        loop_description= {}
        loop_description["type"] = "mcp"
        loop_description["id"] = mcp_id
        loop_description["mcp_name"] = mcp_name
        loop_description["updated_at"] = mcp_updated_at
        loop_description["category"] = mcp_category
        loop_description["name"] = tool["name"]
        loop_description["description"] = tool["description"]
        description_mcps.append(loop_description)

description_workflows = []
workflows = os.listdir("data retrival/workflows")
for workflow in workflows:
    with open(f"data retrival/workflows/{workflow}", "r") as f:
        workflow_data = json.load(f)
    loop_description = {}
    loop_description["type"] = "workflow"
    loop_description["id"] = workflow_data["workflow"]["id"]
    loop_description["name"] = workflow_data["workflow"]["name"]
    loop_description["category"] = workflow_data["workflow"]["category"]
    loop_description["description"] = workflow_data["workflow"]["description"]
    loop_description["updated_at"] = workflow_data["workflow"]["updated_at"]
    description_workflows.append(loop_description)

final_description = description + description_mcps + description_workflows
with open("description.json", "w") as f:
    json.dump(final_description, f, indent=4)

